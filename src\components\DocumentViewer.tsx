import React, { useState, useEffect, useRef } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { TextPosition } from '../types/intelligenceTypes'

interface DocumentViewerProps {
  filePath: string
  fileName: string
  onTextSelection?: (selectedText: string, position: TextPosition) => void
  onContentLoad?: (content: string) => void
}

export const DocumentViewer: React.FC<DocumentViewerProps> = ({
  filePath,
  fileName,
  onTextSelection,
  onContentLoad
}) => {
  const [content, setContent] = useState<string>('')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [zoom, setZoom] = useState(100)
  const [selectedText, setSelectedText] = useState<string>('')
  const contentRef = useRef<HTMLDivElement>(null)

  const fileExtension = fileName.split('.').pop()?.toLowerCase() || ''

  useEffect(() => {
    loadFileContent()
  }, [filePath])

  const loadFileContent = async () => {
    setIsLoading(true)
    setError(null)

    try {
      if (window.electronAPI?.vault?.readFile) {
        const result = await window.electronAPI.vault.readFile(filePath)
        if (result.success && result.content) {
          setContent(result.content)
          onContentLoad?.(result.content)
        } else {
          throw new Error(result.error || 'Failed to read file')
        }
      } else {
        // Fallback for development
        setContent(generateMockContent())
        onContentLoad?.(generateMockContent())
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load file')
    } finally {
      setIsLoading(false)
    }
  }

  const generateMockContent = (): string => {
    if (fileExtension === 'pdf') {
      return `# Project Specification Document

## Overview
This comprehensive project specification document outlines the complete design system requirements for the ChatLo application. It includes detailed UI components, color schemes, typography guidelines, and interaction patterns.

## Key Components

### 1. Design System
- Color palette: Primary #8AB0BB, Secondary #FF8383, Tertiary #1B3E68
- Typography: Inter font family
- Component library with reusable elements

### 2. User Interface Requirements
- Dark theme implementation
- Responsive design patterns
- Accessibility compliance
- Modern interaction patterns

### 3. Technical Specifications
- React-based component architecture
- TypeScript implementation
- Tailwind CSS styling
- FontAwesome icon integration

## Implementation Guidelines
The design system should maintain consistency across all application components while providing flexibility for future enhancements.`
    } else if (fileExtension === 'md') {
      return `# Documentation File

This is a markdown document containing important information about the project.

## Features
- Comprehensive documentation
- Code examples
- API references
- Installation guides

## Usage
Follow the guidelines provided in this document for proper implementation.`
    } else {
      return `File content for ${fileName}

This is the content of the selected file. The actual content would be loaded from the file system.

Key information:
- File type: ${fileExtension}
- File path: ${filePath}
- Content type: Text-based document`
    }
  }

  const handleTextSelection = () => {
    const selection = window.getSelection()
    if (selection && selection.toString().trim()) {
      const selectedText = selection.toString().trim()
      setSelectedText(selectedText)
      
      // Calculate position information
      const range = selection.getRangeAt(0)
      const position: TextPosition = {
        start_offset: range.startOffset,
        end_offset: range.endOffset,
        line_number: 1, // Would need more complex calculation for actual line numbers
        column_number: 1
      }
      
      onTextSelection?.(selectedText, position)
    }
  }

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 200))
  }

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 50))
  }

  const resetZoom = () => {
    setZoom(100)
  }

  const getFileIcon = () => {
    switch (fileExtension) {
      case 'pdf':
        return ICONS.filePdf
      case 'md':
      case 'txt':
        return ICONS.fileText
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'svg':
        return ICONS.fileImage
      case 'js':
      case 'ts':
      case 'jsx':
      case 'tsx':
        return ICONS.fileCode
      default:
        return ICONS.file
    }
  }

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-gray-400">
            <FontAwesomeIcon icon={ICONS.spinner} className="text-4xl mb-4 animate-spin" />
            <p className="text-lg font-medium">Loading document...</p>
            <p className="text-sm">Please wait while we load the content</p>
          </div>
        </div>
      )
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-red-400">
            <FontAwesomeIcon icon={ICONS.exclamationTriangle} className="text-4xl mb-4" />
            <p className="text-lg font-medium">Failed to load document</p>
            <p className="text-sm">{error}</p>
            <button 
              onClick={loadFileContent}
              className="mt-4 px-4 py-2 bg-secondary hover:bg-secondary/80 text-gray-900 rounded-lg transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      )
    }

    if (fileExtension === 'pdf') {
      return (
        <div className="h-full bg-gray-800">
          <iframe
            src={`data:application/pdf;base64,${btoa(content)}`}
            className="w-full h-full border-0 rounded-lg"
            title={fileName}
            style={{ transform: `scale(${zoom / 100})`, transformOrigin: 'top left' }}
          />
        </div>
      )
    }

    // For text-based files
    return (
      <div 
        ref={contentRef}
        className="h-full p-4 bg-white text-gray-900 overflow-auto rounded-lg border border-tertiary/50"
        style={{ fontSize: `${zoom}%` }}
        onMouseUp={handleTextSelection}
      >
        {fileExtension === 'md' ? (
          <div className="prose prose-sm max-w-none">
            <pre className="whitespace-pre-wrap font-mono text-sm">{content}</pre>
          </div>
        ) : (
          <pre className="whitespace-pre-wrap font-mono text-sm leading-relaxed">{content}</pre>
        )}
      </div>
    )
  }

  return (
    <div className="flex-1 bg-gray-800 flex flex-col border-r border-tertiary/50">
      {/* Document Viewer Header */}
      <div className="p-4 border-b border-tertiary/50 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FontAwesomeIcon icon={getFileIcon()} className="text-secondary text-lg" />
          <span className="text-supplement1 font-semibold text-lg">{fileName}</span>
        </div>
        <div className="flex items-center gap-2">
          <button 
            onClick={handleZoomOut}
            className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
            disabled={zoom <= 50}
          >
            <FontAwesomeIcon icon={ICONS.magnifyingGlassMinus} className="text-gray-400 text-sm" />
          </button>
          <button 
            onClick={resetZoom}
            className="px-3 py-1 hover:bg-gray-700 rounded transition-colors"
          >
            <span className="text-gray-400 text-sm">{zoom}%</span>
          </button>
          <button 
            onClick={handleZoomIn}
            className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
            disabled={zoom >= 200}
          >
            <FontAwesomeIcon icon={ICONS.magnifyingGlassPlus} className="text-gray-400 text-sm" />
          </button>
          <button className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
            <FontAwesomeIcon icon={ICONS.download} className="text-gray-400 text-sm" />
          </button>
        </div>
      </div>
      
      {/* Document Content */}
      <div className="flex-1 overflow-hidden bg-gray-900 p-4">
        {renderContent()}
      </div>

      {/* Text Selection Indicator */}
      {selectedText && (
        <div className="p-2 bg-primary/10 border-t border-primary/20">
          <div className="flex items-center gap-2">
            <FontAwesomeIcon icon={ICONS.textSelect} className="text-primary text-sm" />
            <span className="text-primary text-xs font-medium">Selected:</span>
            <span className="text-supplement1 text-xs truncate flex-1">
              "{selectedText.length > 50 ? selectedText.substring(0, 50) + '...' : selectedText}"
            </span>
            <button 
              onClick={() => setSelectedText('')}
              className="p-1 hover:bg-primary/20 rounded transition-colors"
            >
              <FontAwesomeIcon icon={ICONS.xmark} className="text-primary text-xs" />
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
