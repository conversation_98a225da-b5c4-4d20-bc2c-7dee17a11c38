import React, { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { DocumentViewer } from './DocumentViewer'
import { EntityLabelSystem } from './EntityLabelSystem'
import { SmartAnnotationSystem } from './SmartAnnotationSystem'
import { fileIntelligenceService } from '../services/fileIntelligenceService'
import { askAINavigationService } from '../services/askAINavigationService'
import { contextVaultService } from '../services/contextVaultService'
import {
  FileIntelligenceData,
  EntitySelection,
  SmartAnnotation,
  TextPosition
} from '../types/intelligenceTypes'
import { toastService } from '../services/toastService'

interface FileDetailsOverlayProps {
  filePath?: string
  onClose: () => void
}

export const FileDetailsOverlay: React.FC<FileDetailsOverlayProps> = ({
  filePath,
  onClose
}) => {
  const [intelligenceData, setIntelligenceData] = useState<FileIntelligenceData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [selectedText, setSelectedText] = useState<string>('')
  const [selectedTextPosition, setSelectedTextPosition] = useState<TextPosition | undefined>()
  const [currentVault, setCurrentVault] = useState<string>('')
  const [currentVaultPath, setCurrentVaultPath] = useState<string>('')
  const [contextId, setContextId] = useState<string | undefined>()

  if (!filePath) return null

  // Extract file name from path
  const fileName = filePath.split('/').pop() || filePath
  const fileExtension = fileName.split('.').pop()?.toLowerCase() || ''

  useEffect(() => {
    initializeIntelligenceData()
    loadCurrentVaultInfo()
  }, [filePath])

  const initializeIntelligenceData = async () => {
    setIsLoading(true)
    try {
      // Load existing intelligence data or create new
      const existingData = await fileIntelligenceService.loadFileIntelligence(currentVaultPath, filePath!)

      if (existingData) {
        setIntelligenceData(existingData)
      } else {
        // Create initial data with mock entities
        const initialData = fileIntelligenceService.createInitialIntelligenceData(
          filePath!,
          fileName,
          currentVault,
          contextId
        )

        // Generate mock entities for demonstration
        initialData.entity_selections = fileIntelligenceService.generateMockEntities(fileName)

        setIntelligenceData(initialData)

        // Save initial data
        await fileIntelligenceService.saveFileIntelligence(currentVaultPath, initialData)
      }
    } catch (error) {
      console.error('Failed to initialize intelligence data:', error)
      toastService.error('Load Failed', 'Could not load document intelligence data')
    } finally {
      setIsLoading(false)
    }
  }

  const loadCurrentVaultInfo = async () => {
    try {
      // Get current vault information from context vault service
      const vaults = await contextVaultService.loadVaults()
      const selectedVault = vaults.find(v => v.isSelected) || vaults[0]

      if (selectedVault) {
        setCurrentVault(selectedVault.name)
        setCurrentVaultPath(selectedVault.path)
        setContextId(selectedVault.id)
      }
    } catch (error) {
      console.error('Failed to load vault info:', error)
    }
  }

  const handleSmartAnnotation = async () => {
    if (!intelligenceData) return

    setIsAnalyzing(true)
    try {
      // Generate AI analysis (mock implementation)
      const aiAnnotation: SmartAnnotation = {
        annotation_id: `ai_${Date.now()}`,
        content: generateMockAIAnalysis(),
        is_ai_generated: true,
        timestamp: new Date().toISOString(),
        last_edited: new Date().toISOString(),
        tags: ['ai-analysis', 'summary'],
        confidence: 0.85
      }

      await fileIntelligenceService.addSmartAnnotation(currentVaultPath, filePath!, aiAnnotation)

      // Update local state
      setIntelligenceData(prev => prev ? {
        ...prev,
        smart_annotations: [...prev.smart_annotations, aiAnnotation]
      } : null)

      toastService.success('Analysis Complete', 'AI insights have been generated')
    } catch (error) {
      console.error('Smart annotation failed:', error)
      toastService.error('Analysis Failed', 'Could not generate AI insights')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const handleAskAI = async () => {
    if (!intelligenceData) return

    try {
      askAINavigationService.showNavigationSuccess(fileName)

      // Get selected entities for context
      const selectedEntities = intelligenceData.entity_selections
        .filter(e => e.is_selected)
        .map(e => e.entity_text)

      if (selectedText) {
        await askAINavigationService.navigateToChatWithText(
          filePath!,
          fileName,
          currentVault,
          selectedText,
          contextId
        )
      } else if (selectedEntities.length > 0) {
        await askAINavigationService.navigateToChatWithEntities(
          filePath!,
          fileName,
          currentVault,
          selectedEntities,
          contextId
        )
      } else {
        await askAINavigationService.navigateToChat(
          filePath!,
          fileName,
          currentVault,
          contextId
        )
      }
    } catch (error) {
      console.error('Ask AI navigation failed:', error)
    }
  }

  const handleExtractText = () => {
    // Extract text functionality for PDF/images
    console.log('Extract Text triggered for:', fileName)
    toastService.info('Extract Text', 'Text extraction feature coming soon')
  }

  const generateMockAIAnalysis = (): string => {
    const analyses = [
      `This ${fileExtension.toUpperCase()} document contains comprehensive information about ${fileName.replace(/\.[^/.]+$/, "")}. Key insights include structured content organization, technical specifications, and implementation guidelines. The document demonstrates professional documentation standards with clear sections and detailed explanations.`,

      `Analysis of ${fileName} reveals important technical concepts and methodological approaches. The content structure suggests this is a reference document with practical applications. Notable elements include systematic organization, detailed specifications, and actionable information that can guide implementation decisions.`,

      `Document intelligence analysis indicates this file contains valuable knowledge assets. The content demonstrates expertise in the subject matter with well-organized information architecture. Key themes include technical documentation, process guidelines, and structured knowledge representation suitable for context-aware AI assistance.`
    ]

    return analyses[Math.floor(Math.random() * analyses.length)]
  }

  // Get appropriate icon based on file extension
  const getFileIcon = () => {
    switch (fileExtension) {
      case 'pdf':
        return ICONS.filePdf
      case 'md':
      case 'txt':
        return ICONS.fileText
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'svg':
        return ICONS.fileImage
      case 'js':
      case 'ts':
      case 'jsx':
      case 'tsx':
        return ICONS.fileCode
      default:
        return ICONS.file
    }
  }

  // Event handlers for components
  const handleEntityToggle = async (entityId: string, isSelected: boolean) => {
    if (!intelligenceData) return

    try {
      await fileIntelligenceService.updateEntitySelection(currentVaultPath, filePath!, entityId, isSelected)

      // Update local state
      setIntelligenceData(prev => prev ? {
        ...prev,
        entity_selections: prev.entity_selections.map(entity =>
          entity.entity_id === entityId
            ? { ...entity, is_selected: isSelected, selection_timestamp: new Date().toISOString() }
            : entity
        )
      } : null)
    } catch (error) {
      console.error('Failed to update entity selection:', error)
    }
  }

  const handleEntitiesChange = async (entities: EntitySelection[]) => {
    if (!intelligenceData) return

    setIntelligenceData(prev => prev ? {
      ...prev,
      entity_selections: entities
    } : null)
  }

  const handleAnnotationCreate = async (content: string, selectedText?: string, position?: TextPosition) => {
    if (!intelligenceData) return

    try {
      const annotation: SmartAnnotation = {
        annotation_id: `user_${Date.now()}`,
        content,
        is_ai_generated: false,
        timestamp: new Date().toISOString(),
        last_edited: new Date().toISOString(),
        tags: [],
        selected_text: selectedText,
        text_position: position
      }

      await fileIntelligenceService.addSmartAnnotation(currentVaultPath, filePath!, annotation)

      // Update local state
      setIntelligenceData(prev => prev ? {
        ...prev,
        smart_annotations: [...prev.smart_annotations, annotation]
      } : null)

      toastService.success('Annotation Added', 'Your note has been saved')
    } catch (error) {
      console.error('Failed to create annotation:', error)
      toastService.error('Save Failed', 'Could not save annotation')
    }
  }

  const handleAnnotationUpdate = async (annotationId: string, content: string) => {
    if (!intelligenceData) return

    try {
      await fileIntelligenceService.updateSmartAnnotation(currentVaultPath, filePath!, annotationId, content)

      // Update local state
      setIntelligenceData(prev => prev ? {
        ...prev,
        smart_annotations: prev.smart_annotations.map(annotation =>
          annotation.annotation_id === annotationId
            ? { ...annotation, content, last_edited: new Date().toISOString() }
            : annotation
        )
      } : null)

      toastService.success('Annotation Updated', 'Your changes have been saved')
    } catch (error) {
      console.error('Failed to update annotation:', error)
      toastService.error('Update Failed', 'Could not save changes')
    }
  }

  const handleAnnotationDelete = async (annotationId: string) => {
    if (!intelligenceData) return

    try {
      await fileIntelligenceService.deleteSmartAnnotation(currentVaultPath, filePath!, annotationId)

      // Update local state
      setIntelligenceData(prev => prev ? {
        ...prev,
        smart_annotations: prev.smart_annotations.filter(annotation => annotation.annotation_id !== annotationId)
      } : null)

      toastService.success('Annotation Deleted', 'Annotation has been removed')
    } catch (error) {
      console.error('Failed to delete annotation:', error)
      toastService.error('Delete Failed', 'Could not remove annotation')
    }
  }

  const handleTextSelection = (text: string, position: TextPosition) => {
    setSelectedText(text)
    setSelectedTextPosition(position)
  }

  const handleContentLoad = (content: string) => {
    // Update intelligence data with document content if needed
    if (intelligenceData && !intelligenceData.document_content) {
      setIntelligenceData(prev => prev ? {
        ...prev,
        document_content: content.substring(0, 1000) // Store first 1000 chars for context
      } : null)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-neutral-950 border border-tertiary/50 rounded-lg w-full max-w-7xl h-[90vh] flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-tertiary/50 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <FontAwesomeIcon icon={getFileIcon()} className="text-secondary text-xl" />
            <div>
              <h2 className="text-supplement1 font-semibold text-lg">{fileName}</h2>
              <p className="text-gray-400 text-sm">{filePath}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-800 rounded-lg transition-colors"
          >
            <FontAwesomeIcon icon={ICONS.xmark} className="text-gray-400 text-lg" />
          </button>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Panel - Document Viewer */}
          <DocumentViewer
            filePath={filePath!}
            fileName={fileName}
            onTextSelection={handleTextSelection}
            onContentLoad={handleContentLoad}
          />

          {/* Right Panel - Intelligence Collection */}
          <div className="w-96 bg-gray-900 flex flex-col">
            {/* Entity Labels */}
            {intelligenceData && (
              <EntityLabelSystem
                entities={intelligenceData.entity_selections}
                onEntityToggle={handleEntityToggle}
                onEntitiesChange={handleEntitiesChange}
                isLoading={isLoading}
              />
            )}

            {/* Smart Annotation */}
            {intelligenceData && (
              <SmartAnnotationSystem
                annotations={intelligenceData.smart_annotations}
                selectedText={selectedText}
                selectedTextPosition={selectedTextPosition}
                onAnnotationCreate={handleAnnotationCreate}
                onAnnotationUpdate={handleAnnotationUpdate}
                onAnnotationDelete={handleAnnotationDelete}
                onSmartAnalyze={handleSmartAnnotation}
                isAnalyzing={isAnalyzing}
              />
            )}

            {/* Action Buttons */}
            <div className="p-4 space-y-3">
              <button
                onClick={handleAskAI}
                className="w-full bg-secondary hover:bg-secondary/80 text-gray-900 font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
              >
                <FontAwesomeIcon icon={ICONS.comments} />
                Ask AI
              </button>

              <div className="flex gap-2">
                <button
                  onClick={handleExtractText}
                  className="flex-1 bg-gray-700 hover:bg-gray-600 text-supplement1 font-medium py-2 px-3 rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  <FontAwesomeIcon icon={ICONS.fileText} className="text-sm" />
                  Extract
                </button>

                <div className="flex-1 text-xs text-gray-400 px-2 py-2 flex items-center">
                  Vault: {currentVault || 'Default'}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
