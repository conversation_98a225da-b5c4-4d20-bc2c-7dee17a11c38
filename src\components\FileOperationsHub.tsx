import React, { useState, useEffect, useRef, useCallback } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { FilePreviewPane } from './FilePreviewPane'
import { FileActionsPanel } from './FileActionsPanel'
import { FileDetailsOverlay } from './FileDetailsOverlay'

interface FileOperationsHubProps {
  selectedFile?: string
  filePath?: string
  fileContent?: string
  selectedContextId?: string
  onFileAction?: (action: string, data?: any) => void
  onFileLoad?: (content: string) => void
  onFileEdit?: (content: string) => void
  onClose?: () => void
}

export const FileOperationsHub: React.FC<FileOperationsHubProps> = ({
  selectedFile,
  filePath,
  fileContent,
  selectedContextId,
  onFileAction,
  onFileLoad,
  onFileEdit,
  onClose
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [activeTab, setActiveTab] = useState<'preview' | 'actions'>('preview')
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [panelWidth, setPanelWidth] = useState(800) // Default width - larger for better file viewing
  const [isResizing, setIsResizing] = useState(false)
  const [showDetailsOverlay, setShowDetailsOverlay] = useState(false)
  const resizeRef = useRef<HTMLDivElement>(null)
  const panelRef = useRef<HTMLDivElement>(null)

  const MIN_WIDTH = 300
  const MAX_WIDTH = 1200

  // Auto-switch to preview when a file is selected
  useEffect(() => {
    if (selectedFile && activeTab === 'actions') {
      setActiveTab('preview')
    }
  }, [selectedFile])

  // Handle mouse resize
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    setIsResizing(true)
  }, [])

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing) return

    const containerRect = panelRef.current?.parentElement?.getBoundingClientRect()
    if (!containerRect) return

    const newWidth = containerRect.right - e.clientX
    const clampedWidth = Math.max(MIN_WIDTH, Math.min(MAX_WIDTH, newWidth))
    setPanelWidth(clampedWidth)
  }, [isResizing])

  const handleMouseUp = useCallback(() => {
    setIsResizing(false)
  }, [])

  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = 'ew-resize'
      document.body.style.userSelect = 'none'

      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
        document.body.style.cursor = ''
        document.body.style.userSelect = ''
      }
    }
  }, [isResizing, handleMouseMove, handleMouseUp])

  const handleAction = (action: string, data?: any) => {
    console.log('File operation hub action:', action, data)
    
    // Handle built-in actions
    switch (action) {
      case 'copy-path':
        if (data?.path) {
          navigator.clipboard.writeText(data.path)
          // TODO: Show toast notification
        }
        break
      
      case 'copy-content':
        if (fileContent) {
          navigator.clipboard.writeText(fileContent)
          // TODO: Show toast notification
        }
        break
      
      case 'open-external':
        // TODO: Implement system app opening
        console.log('Opening in system app:', data)
        break
      
      case 'show-in-explorer':
        // TODO: Implement explorer reveal
        console.log('Showing in explorer:', data)
        break

      case 'show-details-overlay':
        setShowDetailsOverlay(true)
        break

      default:
        // Pass through to parent handler
        onFileAction?.(action, data)
        break
    }
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  if (!selectedFile) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-900">
        <div className="text-center">
          <FontAwesomeIcon icon={ICONS.file} className="text-gray-400 text-4xl mb-4" />
          <h3 className="text-lg font-medium text-supplement1 mb-2">File Operations Hub</h3>
          <p className="text-sm text-gray-400 mb-4">Select a file to preview and perform operations</p>
          <div className="flex items-center justify-center gap-4 text-xs text-gray-500">
            <div className="flex items-center gap-1">
              <FontAwesomeIcon icon={ICONS.eye} className="text-primary" />
              <span>Preview</span>
            </div>
            <div className="flex items-center gap-1">
              <FontAwesomeIcon icon={ICONS.cog} className="text-primary" />
              <span>Actions</span>
            </div>
            <div className="flex items-center gap-1">
              <FontAwesomeIcon icon={ICONS.brain} className="text-primary" />
              <span>AI Operations</span>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div
      ref={panelRef}
      className={`flex flex-col bg-gray-900 relative ${isFullscreen ? 'fixed inset-0 z-50' : 'flex-1'}`}
      style={{
        width: isFullscreen ? '100%' : isCollapsed ? '48px' : undefined,
        minWidth: isCollapsed ? '48px' : `${MIN_WIDTH}px`,
        maxWidth: isFullscreen ? '100%' : undefined
      }}
    >
      {/* Collapse Handle - positioned on the left edge */}
      {!isFullscreen && (
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="absolute left-2 top-2 w-6 h-6 bg-gray-700 hover:bg-gray-600 rounded border border-gray-600 flex items-center justify-center z-20 transition-colors"
          title={isCollapsed ? 'Expand Panel' : 'Collapse Panel'}
        >
          <FontAwesomeIcon
            icon={isCollapsed ? ICONS.chevronRight : ICONS.chevronLeft}
            className="text-xs text-gray-300"
          />
        </button>
      )}

      {/* Collapsed State */}
      {isCollapsed && !isFullscreen && (
        <div className="flex flex-col items-center py-4 gap-2">
          <FontAwesomeIcon icon={ICONS.file} className="text-primary text-lg" />
          <div className="text-xs text-gray-400 writing-mode-vertical transform rotate-180">
            Hub
          </div>
        </div>
      )}

      {/* Full Content */}
      {!isCollapsed && (
        <>
          {/* Header */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center gap-3">
          <FontAwesomeIcon icon={ICONS.file} className="text-primary text-sm" />
          <div className="min-w-0">
            <div className="text-sm font-medium text-supplement1 truncate" title={selectedFile}>
              {selectedFile}
            </div>
            <div className="text-xs text-gray-400">
              File Operations Hub
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Tab Switcher */}
          <div className="flex bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setActiveTab('preview')}
              className={`px-3 py-1 text-xs rounded transition-colors ${
                activeTab === 'preview'
                  ? 'bg-primary text-gray-900'
                  : 'text-supplement1 hover:text-primary'
              }`}
            >
              <FontAwesomeIcon icon={ICONS.eye} className="mr-1" />
              Preview
            </button>
            <button
              onClick={() => setActiveTab('actions')}
              className={`px-3 py-1 text-xs rounded transition-colors ${
                activeTab === 'actions'
                  ? 'bg-primary text-gray-900'
                  : 'text-supplement1 hover:text-primary'
              }`}
            >
              <FontAwesomeIcon icon={ICONS.cog} className="mr-1" />
              Actions
            </button>
          </div>

          {/* Details Overlay Toggle */}
          <button
            onClick={() => setShowDetailsOverlay(true)}
            className="p-2 text-gray-400 hover:text-primary transition-colors"
            title="Open File Details Overlay"
          >
            <FontAwesomeIcon icon={ICONS.brain} className="text-sm" />
          </button>

          {/* Fullscreen Toggle */}
          <button
            onClick={toggleFullscreen}
            className="p-2 text-gray-400 hover:text-supplement1 transition-colors"
            title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
          >
            <FontAwesomeIcon
              icon={isFullscreen ? ICONS.compress : ICONS.expand}
              className="text-sm"
            />
          </button>

          {/* Close Button */}
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-secondary transition-colors"
            title="Close Hub"
          >
            <FontAwesomeIcon icon={ICONS.times} className="text-sm" />
          </button>

          {/* Close Button (only in fullscreen) */}
          {isFullscreen && (
            <button
              onClick={() => setIsFullscreen(false)}
              className="p-2 text-gray-400 hover:text-secondary transition-colors"
              title="Exit Fullscreen"
            >
              <FontAwesomeIcon icon={ICONS.compress} className="text-sm" />
            </button>
          )}
        </div>
      </div>

      {/* Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {activeTab === 'preview' ? (
          /* Preview Mode - 60% preview, 40% actions */
          <>
            <div className="flex-1 h-full">
              <FilePreviewPane
                selectedFile={selectedFile}
                filePath={filePath}
                fileContent={fileContent}
                onFileLoad={onFileLoad}
                onFileEdit={onFileEdit}
              />
            </div>
            <div className="w-2/5 h-full border-l border-gray-700">
              <FileActionsPanel
                selectedFile={selectedFile}
                filePath={filePath}
                fileContent={fileContent}
                selectedContextId={selectedContextId}
                onAction={handleAction}
              />
            </div>
          </>
        ) : (
          /* Actions Mode - Full width actions */
          <div className="flex-1 h-full">
            <FileActionsPanel
              selectedFile={selectedFile}
              filePath={filePath}
              fileContent={fileContent}
              selectedContextId={selectedContextId}
              onAction={handleAction}
            />
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-4 py-1 bg-gray-800 border-t border-gray-700 text-xs text-gray-400">
        <div className="flex items-center gap-4">
          <span>File: {selectedFile}</span>
          {filePath && <span>Path: {filePath}</span>}
        </div>
        <div className="flex items-center gap-2">
          {selectedContextId && (
            <span className="flex items-center gap-1">
              <FontAwesomeIcon icon={ICONS.cube} className="text-primary" />
              Context Active
            </span>
          )}
          <span>{activeTab === 'preview' ? 'Preview Mode' : 'Actions Mode'}</span>
        </div>
      </div>

        </>
      )} {/* Close !isCollapsed wrapper */}

      {/* File Details Overlay */}
      {showDetailsOverlay && (
        <FileDetailsOverlay
          filePath={filePath || ''}
          onClose={() => setShowDetailsOverlay(false)}
        />
      )}
    </div>
  )
}
