/**
 * Intelligence Collection System Types
 * Defines data structures for document intelligence and user interaction tracking
 */

// Core Intelligence Data Types
export interface DocumentIntelligenceSession {
  session_id: string
  timestamp: string
  document: DocumentMetadata
  intelligence_session: IntelligenceAnalysis
  user_interactions: UserInteraction[]
  context_signals: ContextSignals
  learning_data: LearningData
}

export interface DocumentMetadata {
  path: string
  name: string
  type: string
  size: number
  hash: string
  vault: string
  context_id?: string
  last_modified: string
}

export interface IntelligenceAnalysis {
  session_type: 'smart_annotation' | 'entity_extraction' | 'content_analysis'
  ai_model: string
  processing_time_ms: number
  confidence_score: number
  extracted_entities: ExtractedEntity[]
  key_insights: KeyInsight[]
  content_summary?: string
  related_documents?: string[]
}

export interface ExtractedEntity {
  entity: string
  type: EntityType
  confidence: number
  user_selected: boolean
  context: string
  category?: string
  relationships?: EntityRelationship[]
}

export type EntityType = 
  | 'content_category'
  | 'technical_concept' 
  | 'action_item'
  | 'person'
  | 'organization'
  | 'location'
  | 'date'
  | 'technology'
  | 'methodology'
  | 'requirement'
  | 'feature'
  | 'issue'
  | 'solution'
  | 'other'

export interface EntityRelationship {
  target_entity: string
  relationship_type: 'related_to' | 'part_of' | 'implements' | 'requires' | 'conflicts_with'
  strength: number
}

export interface KeyInsight {
  insight: string
  importance: 'low' | 'medium' | 'high' | 'critical'
  category: 'summary' | 'analysis' | 'recommendation' | 'warning' | 'opportunity'
  confidence: number
  supporting_entities?: string[]
}

export interface UserInteraction {
  action: UserActionType
  timestamp: string
  data?: any
  session_id: string
}

export type UserActionType =
  | 'tag_selection'
  | 'tag_deselection'
  | 'annotation_edit'
  | 'annotation_save'
  | 'smart_annotation_trigger'
  | 'ai_chat_initiate'
  | 'extract_trigger'
  | 'document_open'
  | 'document_close'
  | 'session_export'

export interface ContextSignals {
  user_intent: string
  document_importance: 'low' | 'medium' | 'high' | 'critical'
  workflow_stage: string
  related_documents: string[]
  access_frequency: number
  time_spent_seconds: number
  interaction_depth: 'surface' | 'moderate' | 'deep'
}

export interface LearningData {
  entity_patterns: Record<string, EntityPattern>
  user_preferences: UserPreferences
  workflow_patterns: WorkflowPattern[]
  content_clusters: ContentCluster[]
}

export interface EntityPattern {
  frequency: number
  co_occurrences: string[]
  contexts: string[]
  user_selection_rate: number
  confidence_trend: number[]
}

export interface UserPreferences {
  preferred_tags: string[]
  annotation_style: 'concise' | 'detailed' | 'technical'
  detail_level: 'low' | 'medium' | 'high'
  ai_confidence_threshold: number
  auto_tag_enabled: boolean
}

export interface WorkflowPattern {
  pattern_id: string
  sequence: string[]
  frequency: number
  success_rate: number
  context: string
}

export interface ContentCluster {
  cluster_id: string
  theme: string
  documents: string[]
  entities: string[]
  strength: number
  last_updated: string
}

// Storage Schema Types
export interface IntelligenceIndex {
  version: string
  vault_path: string
  total_documents: number
  total_sessions: number
  last_updated: string
  entity_index: Record<string, EntityIndexEntry>
  document_profiles: Record<string, DocumentProfile>
}

export interface EntityIndexEntry {
  entity: string
  type: EntityType
  total_occurrences: number
  documents: string[]
  user_selection_rate: number
  average_confidence: number
  first_seen: string
  last_seen: string
}

export interface DocumentProfile {
  document_hash: string
  path: string
  total_sessions: number
  key_entities: string[]
  user_engagement_score: number
  content_importance: 'low' | 'medium' | 'high' | 'critical'
  workflow_connections: WorkflowConnection[]
  last_analyzed: string
}

export interface WorkflowConnection {
  target_document: string
  relationship: 'implements' | 'references' | 'extends' | 'conflicts' | 'supports'
  strength: number
  evidence: string[]
}

// Master.md Integration Types
export interface MasterDocumentIntelligence {
  document_intelligence: {
    total_sessions: number
    key_entities: string[]
    user_engagement_score: number
    content_importance: 'low' | 'medium' | 'high' | 'critical'
    workflow_connections: WorkflowConnection[]
    intelligence_summary: string
    last_updated: string
  }
}

// UI Component Types
export interface IntelligenceUIState {
  isAnalyzing: boolean
  currentSession: DocumentIntelligenceSession | null
  suggestedTags: ExtractedEntity[]
  selectedTags: ExtractedEntity[]
  confidenceScore: number
  insights: KeyInsight[]
  sessionHistory: DocumentIntelligenceSession[]
  currentSessionIndex: number
}

export interface SmartAnnotationConfig {
  ai_model: string
  confidence_threshold: number
  max_entities: number
  enable_relationships: boolean
  auto_save: boolean
  include_content_analysis: boolean
}

// Service Interface Types
export interface IntelligenceCollectionService {
  analyzeDocument(filePath: string, content: string, config?: SmartAnnotationConfig): Promise<IntelligenceAnalysis>
  saveSession(session: DocumentIntelligenceSession): Promise<boolean>
  loadSessionHistory(documentHash: string): Promise<DocumentIntelligenceSession[]>
  updateEntityIndex(entities: ExtractedEntity[], documentHash: string): Promise<void>
  generateInsights(content: string, entities: ExtractedEntity[]): Promise<KeyInsight[]>
  exportIntelligenceData(documentHash: string): Promise<any>
}

// Error Types
export interface IntelligenceError {
  code: 'ANALYSIS_FAILED' | 'STORAGE_ERROR' | 'MODEL_UNAVAILABLE' | 'INVALID_DOCUMENT'
  message: string
  details?: any
}
