
# File Details Overlay - Intelligence Collection Feature Specification

## System Architecture Overview

The File Details Overlay serves as ChatLo's primary intelligence collection point for document understanding and context building. This feature transforms passive file viewing into an active learning system that captures user intent, document insights, and behavioral patterns to enhance AI-assisted workflows.

### Core Purpose
- **Intelligence Collection**: Capture document entities, user annotations, and interaction patterns
- **Context Building**: Generate structured data for master.md processing and AI context enhancement
- **Smart Annotation**: Provide AI-powered note-taking with entity recognition and tagging
- **User Journey Mapping**: Track document interaction patterns for workflow optimization

## Feature Components

### 1. Smart Annotation System
**Trigger**: User opens file details overlay (double-click or select file in tree view)
**Behavior**: One-prompt, one-action AI note-taking (not chat experience)
**Output**: Structured annotations displayed in main text box

#### UI Elements:
- **Smart Annotation Button**: Primary action button to trigger AI analysis
- **Annotation Display Area**: Main text box showing AI-generated insights
- **Entity Tags**: Clickable tags for key concepts discovered by AI
- **Confidence Indicators**: Visual indicators showing AI confidence levels

#### Functionality:
```javascript
// Smart Annotation Workflow
1. User clicks "Smart Annotation" button
2. Local AI model (Gemma3) analyzes document content
3. AI discovers key ideas and generates probability-based labels
4. User selects relevant labels from suggested options
5. Selected entities are recorded for learning
6. Results displayed in annotation area
```

### 2. Entity Recognition & Tagging
**Purpose**: Discover and categorize key concepts within documents
**AI Model**: Gemma3 (32k/128k context) for entity extraction

#### Tag Categories:
- **Content Type**: Document classification (specs, design, code, etc.)
- **Key Concepts**: Main topics and themes
- **Technical Terms**: Domain-specific terminology
- **Action Items**: Tasks or requirements identified
- **Relationships**: Connections to other documents/concepts

#### UI Implementation:
- **Suggested Tags**: AI-generated tags with confidence scores
- **User Selection**: Click-to-select interface for tag confirmation
- **Custom Tags**: User-defined tags for specific needs
- **Tag Hierarchy**: Nested tag structure for complex categorization

### 3. Document Intelligence Panel
**Location**: Right panel of file details overlay
**Purpose**: Display AI insights and user annotations

#### Panel Sections:
- **Quick Insights**: AI-generated document summary
- **Key Entities**: Extracted concepts and terms
- **Related Documents**: Connections to other vault files
- **User Notes**: Manual annotations and comments
- **Interaction History**: Previous analysis sessions

### 4. Action Buttons
#### AI Chat Button
- **Function**: Send document to Chat Page for interactive discussion
- **Behavior**: Creates new chat with document attached and parsed
- **Data Collection**: Records chat initiation event

#### Extract Button
- **Compatibility**: PDF and image files only (Gemma3 compatible)
- **Function**: Extract text/data from visual documents
- **Output**: Structured text data for further processing

## Data Collection Specification

### JSON Data Structure
```json
{
  "session_id": "uuid-v4",
  "timestamp": "2024-01-15T12:00:00Z",
  "document": {
    "path": "/vault/documents/project-spec.pdf",
    "name": "project-spec.pdf",
    "type": "pdf",
    "size": 2048576,
    "hash": "sha256-hash",
    "vault": "Your First Context Vault"
  },
  "intelligence_session": {
    "session_type": "smart_annotation",
    "ai_model": "gemma3-32k",
    "processing_time_ms": 1250,
    "confidence_score": 0.87,
    "extracted_entities": [
      {
        "entity": "UI Design",
        "type": "content_category",
        "confidence": 0.95,
        "user_selected": true,
        "context": "Document contains extensive UI design specifications"
      },
      {
        "entity": "Component Library",
        "type": "technical_concept",
        "confidence": 0.82,
        "user_selected": true,
        "context": "References to reusable UI components"
      }
    ],
    "key_insights": [
      {
        "insight": "Document outlines comprehensive design system",
        "importance": "high",
        "category": "summary"
      }
    ]
  },
  "user_interactions": [
    {
      "action": "tag_selection",
      "timestamp": "2024-01-15T12:01:30Z",
      "selected_tags": ["UI Design", "Specifications", "Requirements"],
      "rejected_tags": ["Architecture", "Testing"]
    },
    {
      "action": "annotation_edit",
      "timestamp": "2024-01-15T12:02:15Z",
      "content": "Key focus on component reusability",
      "type": "user_note"
    }
  ],
  "context_signals": {
    "user_intent": "design_system_review",
    "document_importance": "high",
    "workflow_stage": "specification_analysis",
    "related_documents": [
      "/vault/documents/design-tokens.json",
      "/vault/documents/component-specs.md"
    ]
  },
  "learning_data": {
    "entity_patterns": {
      "design_system": {
        "frequency": 15,
        "co_occurrences": ["components", "tokens", "specifications"]
      }
    },
    "user_preferences": {
      "preferred_tags": ["UI Design", "Specifications"],
      "annotation_style": "concise",
      "detail_level": "high"
    }
  }
}
```

### Data Storage Location
**Path Pattern**: `<vault_folder>/.intelligence/<document_hash>/`
**Files**:
- `session_<timestamp>.json` - Individual session data
- `entity_index.json` - Aggregated entity data
- `learning_patterns.json` - User behavior patterns
- `document_profile.json` - Document-specific insights

### Master.md Integration Data
```json
{
  "document_intelligence": {
    "total_sessions": 5,
    "key_entities": ["UI Design", "Component Library", "Design Tokens"],
    "user_engagement_score": 0.92,
    "content_importance": "high",
    "workflow_connections": [
      {
        "target_document": "design-tokens.json",
        "relationship": "implements",
        "strength": 0.85
      }
    ]
  }
}
```

Visual design:
uxpilot-design-1753981239572.png

### Code example:

```
<html><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>tailwind.config = {
  "theme": {
    "extend": {
      "colors": {
        "primary": "#8AB0BB",
        "secondary": "#FF8383",
        "tertiary": "#1B3E68",
        "supplement1": "#D5D8E0",
        "supplement2": "#89AFBA"
      },
      "fontFamily": {
        "sans": [
          "Inter",
          "sans-serif"
        ]
      }
    }
  }
};</script>
    <script> window.FontAwesomeConfig = { autoReplaceSvg: 'nest'};</script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <style>
        ::-webkit-scrollbar { display: none;}
        body { font-family: 'Inter', sans-serif; }
        .file-tree-item {
            transition: all 0.2s ease;
        }
        .file-tree-item:hover {
            background-color: rgba(138, 176, 187, 0.1);
        }
        .file-tree-item.selected {
            background-color: rgba(138, 176, 187, 0.2);
            border-left: 2px solid #8AB0BB;
        }
        .table-row:hover {
            background-color: rgba(138, 176, 187, 0.1);
        }
    </style>
<link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&amp;display=swap"><style>
  .highlighted-section {
    outline: 2px solid #3F20FB;
    background-color: rgba(63, 32, 251, 0.1);
  }

  .edit-button {
    position: absolute;
    z-index: 1000;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  html, body {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  </style></head><body class="bg-gray-900 text-white overflow-hidden">
    
    <!-- Window Top Bar -->
    <div id="window-top-bar" class="h-6 bg-gray-950 flex items-center justify-end px-2 border-b border-gray-800">
        <div class="flex items-center gap-1">
            <button class="w-4 h-4 flex items-center justify-center hover:bg-gray-800 rounded transition-colors">
                <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-expand" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="expand" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M32 32C14.3 32 0 46.3 0 64v96c0 17.7 14.3 32 32 32s32-14.3 32-32V96h64c17.7 0 32-14.3 32-32s-14.3-32-32-32H32zM64 352c0-17.7-14.3-32-32-32s-32 14.3-32 32v96c0 17.7 14.3 32 32 32h96c17.7 0 32-14.3 32-32s-14.3-32-32-32H64V352zM320 32c-17.7 0-32 14.3-32 32s14.3 32 32 32h64v64c0 17.7 14.3 32 32 32s32-14.3 32-32V64c0-17.7-14.3-32-32-32H320zM448 352c0-17.7-14.3-32-32-32s-32 14.3-32 32v64H320c-17.7 0-32 14.3-32 32s14.3 32 32 32h96c17.7 0 32-14.3 32-32V352z"></path></svg></i>
            </button>
            <button class="w-4 h-4 flex items-center justify-center hover:bg-red-600 rounded transition-colors">
                <i class="text-gray-400 text-xs hover:text-white" data-fa-i2svg=""><svg class="svg-inline--fa fa-xmark" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="xmark" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"></path></svg></i>
            </button>
        </div>
    </div>
    
    <!-- Top Navigation Bar -->
    <div id="top-nav" class="h-12 bg-gray-800 border-b border-tertiary flex items-center px-4">
        <div class="flex items-center gap-2">
            <div class="w-6 h-6 bg-primary rounded flex items-center justify-center">
                <i class="text-gray-900 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-comment" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="comment" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M512 240c0 114.9-114.6 208-256 208c-37.1 0-72.3-6.4-104.1-17.9c-11.9 8.7-31.3 20.6-54.3 30.6C73.6 471.1 44.7 480 16 480c-6.5 0-12.3-3.9-14.8-9.9c-2.5-6-1.1-12.8 3.4-17.4l0 0 0 0 0 0 0 0 .3-.3c.3-.3 .7-.7 1.3-1.4c1.1-1.2 2.8-3.1 4.9-5.7c4.1-5 9.6-12.4 15.2-21.6c10-16.6 19.5-38.4 21.4-62.9C17.7 326.8 0 285.1 0 240C0 125.1 114.6 32 256 32s256 93.1 256 208z"></path></svg></i>
            </div>
            <span class="text-sm font-semibold text-primary">Chatlo</span>
        </div>
        
        <div class="flex-1 flex items-center justify-center ml-12 mr-[calc(256px-48px)]">
            <div class="flex items-center gap-2 bg-tertiary/40 rounded-lg px-3 py-1 w-full max-w-2xl">
                <button class="p-1 hover:bg-tertiary/60 rounded transition-colors">
                    <i class="text-supplement2 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-bell" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bell" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M224 0c-17.7 0-32 14.3-32 32V51.2C119 66 64 130.6 64 208v18.8c0 47-17.3 92.4-48.5 127.6l-7.4 8.3c-8.4 9.4-10.4 22.9-5.3 34.4S19.4 416 32 416H416c12.6 0 24-7.4 29.2-18.9s3.1-25-5.3-34.4l-7.4-8.3C401.3 319.2 384 273.9 384 226.8V208c0-77.4-55-142-128-156.8V32c0-17.7-14.3-32-32-32zm45.3 493.3c12-12 18.7-28.3 18.7-45.3H224 160c0 17 6.7 33.3 18.7 45.3s28.3 18.7 45.3 18.7s33.3-6.7 45.3-18.7z"></path></svg></i>
                </button>
                <button class="p-1 hover:bg-tertiary/60 rounded transition-colors bg-primary/20 border border-primary/30">
                    <i class="text-primary text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-file" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="file" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M0 64C0 28.7 28.7 0 64 0H224V128c0 17.7 14.3 32 32 32H384V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V64zm384 64H256V0L384 128z"></path></svg></i>
                </button>
                <button class="p-1 hover:bg-tertiary/60 rounded transition-colors">
                    <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-left" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-left" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" data-fa-i2svg=""><path fill="currentColor" d="M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l192 192c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256 246.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-192 192z"></path></svg></i>
                </button>
                <div class="flex items-center gap-2 flex-1">
                    <i class="text-supplement2 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-folder" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="folder" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M64 480H448c35.3 0 64-28.7 64-64V160c0-35.3-28.7-64-64-64H288c-10.1 0-19.6-4.7-25.6-12.8L243.2 57.6C231.1 41.5 212.1 32 192 32H64C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64z"></path></svg></i>
                    <span class="text-xs text-supplement1">Project Alpha - Design System</span>
                </div>
                <button class="p-1 hover:bg-tertiary/60 rounded transition-colors">
                    <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-right" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-right" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" data-fa-i2svg=""><path fill="currentColor" d="M310.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L242.7 256 73.4 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z"></path></svg></i>
                </button>
            </div>
        </div>
        
        <div class="flex items-center gap-3">
            <div class="flex items-center gap-2">
                <span class="text-xs text-supplement1">Private</span>
                <button class="relative inline-flex h-4 w-7 items-center rounded-full bg-secondary transition-colors">
                    <span class="inline-block h-3 w-3 transform rounded-full bg-white transition-transform translate-x-3.5"></span>
                </button>
            </div>
            <button class="p-2 hover:bg-gray-700 rounded-lg transition-colors group relative">
                <i class="text-supplement1 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-user" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="user" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512H418.3c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304H178.3z"></path></svg></i>
                <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    User Profile
                </div>
            </button>
            <button class="p-2 hover:bg-gray-700 rounded-lg transition-colors group relative">
                <i class="text-supplement1 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-gear" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="gear" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M495.9 166.6c3.2 8.7 .5 18.4-6.4 24.6l-43.3 39.4c1.1 8.3 1.7 16.8 1.7 25.4s-.6 17.1-1.7 25.4l43.3 39.4c6.9 6.2 9.6 15.9 6.4 24.6c-4.4 11.9-9.7 23.3-15.8 34.3l-4.7 8.1c-6.6 11-14 21.4-22.1 31.2c-5.9 7.2-15.7 9.6-24.5 6.8l-55.7-17.7c-13.4 10.3-28.2 18.9-44 25.4l-12.5 57.1c-2 9.1-9 16.3-18.2 17.8c-13.8 2.3-28 3.5-42.5 3.5s-28.7-1.2-42.5-3.5c-9.2-1.5-16.2-8.7-18.2-17.8l-12.5-57.1c-15.8-6.5-30.6-15.1-44-25.4L83.1 425.9c-8.8 2.8-18.6 .3-24.5-6.8c-8.1-9.8-15.5-20.2-22.1-31.2l-4.7-8.1c-6.1-11-11.4-22.4-15.8-34.3c-3.2-8.7-.5-18.4 6.4-24.6l43.3-39.4C64.6 273.1 64 264.6 64 256s.6-17.1 1.7-25.4L22.4 191.2c-6.9-6.2-9.6-15.9-6.4-24.6c4.4-11.9 9.7-23.3 15.8-34.3l4.7-8.1c6.6-11 14-21.4 22.1-31.2c5.9-7.2 15.7-9.6 24.5-6.8l55.7 17.7c13.4-10.3 28.2-18.9 44-25.4l12.5-57.1c2-9.1 9-16.3 18.2-17.8C227.3 1.2 241.5 0 256 0s28.7 1.2 42.5 3.5c9.2 1.5 16.2 8.7 18.2 17.8l12.5 57.1c15.8 6.5 30.6 15.1 44 25.4l55.7-17.7c8.8-2.8 18.6-.3 24.5 6.8c8.1 9.8 15.5 20.2 22.1 31.2l4.7 8.1c6.1 11 11.4 22.4 15.8 34.3zM256 336a80 80 0 1 0 0-160 80 80 0 1 0 0 160z"></path></svg></i>
                <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    Settings
                </div>
            </button>
        </div>
    </div>
    
    <div id="app-container" class="flex h-[calc(100vh-72px)]">
        
        <!-- VSCode-style Icon Bar -->
        <div id="icon-bar" class="w-12 bg-gray-900 border-r border-tertiary flex flex-col items-center py-2">
            <div class="flex flex-col gap-1 mb-auto">
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                    <i data-fa-i2svg=""><svg class="svg-inline--fa fa-house" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="house" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" data-fa-i2svg=""><path fill="currentColor" d="M575.8 255.5c0 18-15 32.1-32 32.1h-32l.7 160.2c0 2.7-.2 5.4-.5 8.1V472c0 22.1-17.9 40-40 40H456c-1.1 0-2.2 0-3.3-.1c-1.4 .1-2.8 .1-4.2 .1H416 392c-22.1 0-40-17.9-40-40V448 384c0-17.7-14.3-32-32-32H256c-17.7 0-32 14.3-32 32v64 24c0 22.1-17.9 40-40 40H160 128.1c-1.5 0-3-.1-4.5-.2c-1.2 .1-2.4 .2-3.6 .2H104c-22.1 0-40-17.9-40-40V360c0-.9 0-1.9 .1-2.8V287.6H32c-18 0-32-14-32-32.1c0-9 3-17 10-24L266.4 8c7-7 15-8 22-8s15 2 21 7L564.8 231.5c8 7 12 15 11 24z"></path></svg></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Home
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                    <i data-fa-i2svg=""><svg class="svg-inline--fa fa-comment" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="comment" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M512 240c0 114.9-114.6 208-256 208c-37.1 0-72.3-6.4-104.1-17.9c-11.9 8.7-31.3 20.6-54.3 30.6C73.6 471.1 44.7 480 16 480c-6.5 0-12.3-3.9-14.8-9.9c-2.5-6-1.1-12.8 3.4-17.4l0 0 0 0 0 0 0 0 .3-.3c.3-.3 .7-.7 1.3-1.4c1.1-1.2 2.8-3.1 4.9-5.7c4.1-5 9.6-12.4 15.2-21.6c10-16.6 19.5-38.4 21.4-62.9C17.7 326.8 0 285.1 0 240C0 125.1 114.6 32 256 32s256 93.1 256 208z"></path></svg></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Chat
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                    <i data-fa-i2svg=""><svg class="svg-inline--fa fa-clock-rotate-left" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="clock-rotate-left" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M75 75L41 41C25.9 25.9 0 36.6 0 57.9V168c0 13.3 10.7 24 24 24H134.1c21.4 0 32.1-25.9 17-41l-30.8-30.8C155 85.5 203 64 256 64c106 0 192 86 192 192s-86 192-192 192c-40.8 0-78.6-12.7-109.7-34.4c-14.5-10.1-34.4-6.6-44.6 7.9s-6.6 34.4 7.9 44.6C151.2 495 201.7 512 256 512c141.4 0 256-114.6 256-256S397.4 0 256 0C185.3 0 121.3 28.7 75 75zm181 53c-13.3 0-24 10.7-24 24V256c0 6.4 2.5 12.5 7 17l72 72c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-65-65V152c0-13.3-10.7-24-24-24z"></path></svg></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        History
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative bg-primary/20 border-l-2 border-primary">
                    <i class="text-primary" data-fa-i2svg=""><svg class="svg-inline--fa fa-folder-tree" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="folder-tree" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" data-fa-i2svg=""><path fill="currentColor" d="M64 32C64 14.3 49.7 0 32 0S0 14.3 0 32v96V384c0 35.3 28.7 64 64 64H256V384H64V160H256V96H64V32zM288 192c0 17.7 14.3 32 32 32H544c17.7 0 32-14.3 32-32V64c0-17.7-14.3-32-32-32H445.3c-8.5 0-16.6-3.4-22.6-9.4L409.4 9.4c-6-6-14.1-9.4-22.6-9.4H320c-17.7 0-32 14.3-32 32V192zm0 288c0 17.7 14.3 32 32 32H544c17.7 0 32-14.3 32-32V352c0-17.7-14.3-32-32-32H445.3c-8.5 0-16.6-3.4-22.6-9.4l-13.3-13.3c-6-6-14.1-9.4-22.6-9.4H320c-17.7 0-32 14.3-32 32V480z"></path></svg></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Files
                    </div>
                </button>
            </div>
        </div>
        
        <!-- Main Files Content -->
        <div id="files-content" class="flex-1 flex bg-gray-900">
            
            <!-- Left Column - File Tree (20%) -->
            <div id="file-tree-panel" class="w-1/5 bg-gray-800 border-r border-tertiary/50 flex flex-col">
                
                <!-- File Tree Header -->
                <div class="p-4 border-b border-tertiary/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2 flex-1">
                            <button class="p-1 hover:bg-gray-700 rounded transition-colors">
                                <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-left" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-left" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" data-fa-i2svg=""><path fill="currentColor" d="M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l192 192c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256 246.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-192 192z"></path></svg></i>
                            </button>
                            <select class="bg-transparent text-supplement1 text-sm font-medium border-none outline-none flex-1 cursor-pointer">
                                <option class="bg-gray-800 text-supplement1">Your First Context Vault</option>
                                <option class="bg-gray-800 text-supplement1">Project Beta Vault</option>
                                <option class="bg-gray-800 text-supplement1">Design System Vault</option>
                            </select>
                            <button class="p-1 hover:bg-gray-700 rounded transition-colors">
                                <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-right" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-right" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" data-fa-i2svg=""><path fill="currentColor" d="M310.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L242.7 256 73.4 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z"></path></svg></i>
                            </button>
                        </div>
                        <button class="p-1 hover:bg-gray-700 rounded transition-colors group relative ml-2">
                            <i class="text-gray-400 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-plus" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="plus" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M256 80c0-17.7-14.3-32-32-32s-32 14.3-32 32V224H48c-17.7 0-32 14.3-32 32s14.3 32 32 32H192V432c0 17.7 14.3 32 32 32s32-14.3 32-32V288H400c17.7 0 32-14.3 32-32s-14.3-32-32-32H256V80z"></path></svg></i>
                            <div class="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                Add File
                            </div>
                        </button>
                    </div>
                </div>

                <!-- View Toggle Buttons -->
                <div class="p-3 border-b border-tertiary/50">
                    <div class="flex gap-2">
                        <button class="flex-1 flex items-center justify-center gap-2 p-2 rounded-lg bg-secondary text-gray-900 hover:bg-secondary/80 transition-colors">
                            <i class="text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-sitemap" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="sitemap" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" data-fa-i2svg=""><path fill="currentColor" d="M208 80c0-26.5 21.5-48 48-48h64c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48h-8v40H464c30.9 0 56 25.1 56 56v32h8c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48H464c-26.5 0-48-21.5-48-48V368c0-26.5 21.5-48 48-48h8V288c0-4.4-3.6-8-8-8H312v40h8c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48H256c-26.5 0-48-21.5-48-48V368c0-26.5 21.5-48 48-48h8V280H112c-4.4 0-8 3.6-8 8v32h8c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V368c0-26.5 21.5-48 48-48h8V288c0-30.9 25.1-56 56-56H264V192h-8c-26.5 0-48-21.5-48-48V80z"></path></svg></i>
                            <span class="text-xs font-medium">Explorer</span>
                        </button>
                        <button class="flex-1 flex items-center justify-center gap-2 p-2 rounded-lg bg-gray-700/50 hover:bg-gray-700 transition-colors text-supplement1">
                            <i class="text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-lightbulb" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="lightbulb" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M272 384c9.6-31.9 29.5-59.1 49.2-86.2l0 0c5.2-7.1 10.4-14.2 15.4-21.4c19.8-28.5 31.4-63 31.4-100.3C368 78.8 289.2 0 192 0S16 78.8 16 176c0 37.3 11.6 71.9 31.4 100.3c5 7.2 10.2 14.3 15.4 21.4l0 0c19.8 27.1 39.7 54.4 49.2 86.2H272zM192 512c44.2 0 80-35.8 80-80V416H112v16c0 44.2 35.8 80 80 80zM112 176c0 8.8-7.2 16-16 16s-16-7.2-16-16c0-61.9 50.1-112 112-112c8.8 0 16 7.2 16 16s-7.2 16-16 16c-44.2 0-80 35.8-80 80z"></path></svg></i>
                            <span class="text-xs font-medium">Master</span>
                        </button>
                    </div>
                </div>
                
                <!-- File Tree -->
                <div id="file-tree" class="flex-1 overflow-y-auto p-2">
                    
                    <!-- Root Folder -->
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2" onclick="toggleFolder('root')">
                        <i class="text-gray-400 text-xs w-3" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-down" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-down" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"></path></svg></i>
                        <i class="text-supplement2 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-folder" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="folder" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M64 480H448c35.3 0 64-28.7 64-64V160c0-35.3-28.7-64-64-64H288c-10.1 0-19.6-4.7-25.6-12.8L243.2 57.6C231.1 41.5 212.1 32 192 32H64C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64z"></path></svg></i>
                        <span class="text-sm text-supplement1">You First Context Vault</span>
                        <div class="ml-auto">
                            <span class="w-5 h-5 bg-secondary/20 text-secondary text-xs rounded-full flex items-center justify-center font-medium">3</span>
                        </div>
                    </div>
                    
                    <!-- Master.md File -->
                    <div class="file-tree-item selected p-2 rounded cursor-pointer flex items-center gap-2 ml-4 bg-primary/20 border border-primary/30" onclick="selectFile('master.md')">
                        <div class="w-3"></div>
                        <i class="text-primary text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-file-lines" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="file-lines" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M64 0C28.7 0 0 28.7 0 64V448c0 35.3 28.7 64 64 64H320c35.3 0 64-28.7 64-64V160H256c-17.7 0-32-14.3-32-32V0H64zM256 0V128H384L256 0zM112 256H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16z"></path></svg></i>
                        <span class="text-sm text-primary font-medium">master.md</span>
                        <div class="ml-auto">
                            <div class="w-2 h-2 bg-primary rounded-full"></div>
                        </div>
                    </div>
                    
                    <!-- Design Folder -->
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ml-4" onclick="toggleFolder('design')">
                        <i class="text-gray-400 text-xs w-3" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-right" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-right" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" data-fa-i2svg=""><path fill="currentColor" d="M310.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L242.7 256 73.4 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z"></path></svg></i>
                        <i class="text-supplement2 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-folder" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="folder" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M64 480H448c35.3 0 64-28.7 64-64V160c0-35.3-28.7-64-64-64H288c-10.1 0-19.6-4.7-25.6-12.8L243.2 57.6C231.1 41.5 212.1 32 192 32H64C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64z"></path></svg></i>
                        <span class="text-sm text-supplement1">artifacts</span>
                        <div class="ml-auto">
                            <span class="w-4 h-4 bg-supplement2/20 text-supplement2 text-xs rounded-full flex items-center justify-center font-medium">2</span>
                        </div>
                    </div>
                    
                    <!-- Components Folder -->
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ml-4" onclick="toggleFolder('components')">
                        <i class="text-gray-400 text-xs w-3" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-down" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-down" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"></path></svg></i>
                        <i class="text-supplement2 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-folder" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="folder" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M64 480H448c35.3 0 64-28.7 64-64V160c0-35.3-28.7-64-64-64H288c-10.1 0-19.6-4.7-25.6-12.8L243.2 57.6C231.1 41.5 212.1 32 192 32H64C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64z"></path></svg></i>
                        <span class="text-sm text-supplement1">documents</span>
                        <div class="ml-auto">
                            <span class="w-4 h-4 bg-supplement2/20 text-supplement2 text-xs rounded-full flex items-center justify-center font-medium">2</span>
                        </div>
                    </div>
                    
                    <!-- Component Files -->
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ml-8" onclick="selectFile('buttons.md')">
                        <div class="w-3"></div>
                        <i class="text-secondary text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-file-lines" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="file-lines" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M64 0C28.7 0 0 28.7 0 64V448c0 35.3 28.7 64 64 64H320c35.3 0 64-28.7 64-64V160H256c-17.7 0-32-14.3-32-32V0H64zM256 0V128H384L256 0zM112 256H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16z"></path></svg></i>
                        <span class="text-sm text-secondary">buttons.md</span>
                        <div class="ml-auto">
                            <div class="w-2 h-2 bg-secondary rounded-full"></div>
                        </div>
                    </div>
                    
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ml-8" onclick="selectFile('forms.md')">
                        <div class="w-3"></div>
                        <i class="text-gray-400 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-file-lines" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="file-lines" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M64 0C28.7 0 0 28.7 0 64V448c0 35.3 28.7 64 64 64H320c35.3 0 64-28.7 64-64V160H256c-17.7 0-32-14.3-32-32V0H64zM256 0V128H384L256 0zM112 256H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64H272c8.8 0 16 7.2 16 16s-7.2 16-16 16H112c-8.8 0-16-7.2-16-16s7.2-16 16-16z"></path></svg></i>
                        <span class="text-sm text-gray-300">forms.md</span>
                    </div>
                    
                    <!-- Tokens File -->
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ml-8" onclick="selectFile('tokens.json')">
                        <div class="w-3"></div>
                        <i class="text-supplement2 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-file-code" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="file-code" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M64 0C28.7 0 0 28.7 0 64V448c0 35.3 28.7 64 64 64H320c35.3 0 64-28.7 64-64V160H256c-17.7 0-32-14.3-32-32V0H64zM256 0V128H384L256 0zM153 289l-31 31 31 31c9.4 9.4 9.4 24.6 0 33.9s-24.6 9.4-33.9 0L71 337c-9.4-9.4-9.4-24.6 0-33.9l48-48c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9zM265 255l48 48c9.4 9.4 9.4 24.6 0 33.9l-48 48c-9.4 9.4-24.6 9.4-33.9 0s-9.4-24.6 0-33.9l31-31-31-31c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0z"></path></svg></i>
                        <span class="text-sm text-supplement1">tokens.json</span>
                    </div>
                    
                    <!-- Documentation Folder -->
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ml-4" onclick="toggleFolder('docs')">
                        <i class="text-gray-400 text-xs w-3" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-right" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-right" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" data-fa-i2svg=""><path fill="currentColor" d="M310.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L242.7 256 73.4 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z"></path></svg></i>
                        <i class="text-supplement2 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-folder" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="folder" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M64 480H448c35.3 0 64-28.7 64-64V160c0-35.3-28.7-64-64-64H288c-10.1 0-19.6-4.7-25.6-12.8L243.2 57.6C231.1 41.5 212.1 32 192 32H64C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64z"></path></svg></i>
                        <span class="text-sm text-supplement1">images</span>
                    </div>
                </div>
            </div>
            
            <!-- Right Column (80%) - File Explorer -->
            <div id="main-panel" class="flex-1 bg-gray-900 flex">
    
    <!-- PDF Viewer Overlay -->
    <div id="pdf-viewer-overlay" class="absolute inset-0 bg-gray-900 z-50 flex">
        
        <!-- Center Column - PDF Viewer -->
        <div id="pdf-viewer-panel" class="flex-1 bg-gray-800 flex flex-col border-r border-tertiary/50">
            
            <!-- PDF Viewer Header -->
            <div class="p-4 border-b border-tertiary/50 flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <button onclick="closePDFViewer()" class="p-2 hover:bg-gray-700 rounded-lg transition-colors">
                        <i class="text-gray-400 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-arrow-left" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="arrow-left" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l160 160c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.2 288 416 288c17.7 0 32-14.3 32-32s-14.3-32-32-32l-306.7 0L214.6 118.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-160 160z"></path></svg></i>
                    </button>
                    <i class="text-secondary text-lg" data-fa-i2svg=""><svg class="svg-inline--fa fa-file-pdf" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="file-pdf" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M0 64C0 28.7 28.7 0 64 0H224V128c0 17.7 14.3 32 32 32H384V304H176c-35.3 0-64 28.7-64 64V512H64c-35.3 0-64-28.7-64-64V64zm384 64H256V0L384 128zM176 352h32c30.9 0 56 25.1 56 56s-25.1 56-56 56H192v32c0 8.8-7.2 16-16 16s-16-7.2-16-16V448 368c0-8.8 7.2-16 16-16zm32 80c13.3 0 24-10.7 24-24s-10.7-24-24-24H192v48h16zm96-80h32c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48H304c-8.8 0-16-7.2-16-16V368c0-8.8 7.2-16 16-16zm32 128c8.8 0 16-7.2 16-16V400c0-8.8-7.2-16-16-16H320v96h16zm80-112c0-8.8 7.2-16 16-16h48c8.8 0 16 7.2 16 16s-7.2 16-16 16H448v32h32c8.8 0 16 7.2 16 16s-7.2 16-16 16H448v48c0 8.8-7.2 16-16 16s-16-7.2-16-16V432 368z"></path></svg></i>
                    <span class="text-supplement1 font-semibold text-lg">project-spec.pdf</span>
                </div>
                <div class="flex items-center gap-2">
                    <button class="p-2 hover:bg-gray-700 rounded-lg transition-colors">
                        <i class="text-gray-400 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-magnifying-glass-minus" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="magnifying-glass-minus" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L330.7 376c-34.4 25.2-76.8 40-122.7 40C93.1 416 0 322.9 0 208S93.1 0 208 0S416 93.1 416 208zM136 184c-13.3 0-24 10.7-24 24s10.7 24 24 24H280c13.3 0 24-10.7 24-24s-10.7-24-24-24H136z"></path></svg></i>
                    </button>
                    <span class="text-gray-400 text-sm">100%</span>
                    <button class="p-2 hover:bg-gray-700 rounded-lg transition-colors">
                        <i class="text-gray-400 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-magnifying-glass-plus" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="magnifying-glass-plus" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L330.7 376c-34.4 25.2-76.8 40-122.7 40C93.1 416 0 322.9 0 208S93.1 0 208 0S416 93.1 416 208zM184 296c0 13.3 10.7 24 24 24s24-10.7 24-24V232h64c13.3 0 24-10.7 24-24s-10.7-24-24-24H232V120c0-13.3-10.7-24-24-24s-24 10.7-24 24v64H120c-13.3 0-24 10.7-24 24s10.7 24 24 24h64v64z"></path></svg></i>
                    </button>
                    <button class="p-2 hover:bg-gray-700 rounded-lg transition-colors">
                        <i class="text-gray-400 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-download" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="download" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M288 32c0-17.7-14.3-32-32-32s-32 14.3-32 32V274.7l-73.4-73.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l128 128c12.5 12.5 32.8 12.5 45.3 0l128-128c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L288 274.7V32zM64 352c-35.3 0-64 28.7-64 64v32c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V416c0-35.3-28.7-64-64-64H346.5l-45.3 45.3c-25 25-65.5 25-90.5 0L165.5 352H64zm368 56a24 24 0 1 1 0 48 24 24 0 1 1 0-48z"></path></svg></i>
                    </button>
                </div>
            </div>
            
            <!-- PDF Content -->
            <div id="pdf-content" class="flex-1 overflow-y-auto bg-gray-900 p-4">
                <iframe id="pdf-frame" src="data:application/pdf;base64,JVBERi0xLjQKJdPr6eEKMSAwIG9iago8PAovVGl0bGUgKFByb2plY3QgU3BlY2lmaWNhdGlvbikKL0NyZWF0b3IgKERlc2lnbiBUZWFtKQovUHJvZHVjZXIgKFBERiBHZW5lcmF0b3IpCi9DcmVhdGlvbkRhdGUgKEQ6MjAyNDAxMTUxMjAwMDBaKQo+PgplbmRvYmoKMiAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMyAwIFIKPj4KZW5kb2JqCjMgMCBvYmoKPDwKL1R5cGUgL1BhZ2VzCi9LaWRzIFs0IDAgUl0KL0NvdW50IDEKL01lZGlhQm94IFswIDAgNjEyIDc5Ml0KPj4KZW5kb2JqCjQgMCBvYmoKPDwKL1R5cGUgL1BhZ2UKL1BhcmVudCAzIDAgUgovUmVzb3VyY2VzIDw8Ci9Gb250IDw8Ci9GMSA1IDAgUgo+Pgo+PgovQ29udGVudHMgNiAwIFIKPj4KZW5kb2JqCjUgMCBvYmoKPDwKL1R5cGUgL0ZvbnQKL1N1YnR5cGUgL1R5cGUxCi9CYXNlRm9udCAvSGVsdmV0aWNhCj4+CmVuZG9iago2IDAgb2JqCjw8Ci9MZW5ndGggNDQKPj4Kc3RyZWFtCkJUCi9GMSAxMiBUZgo1MCA3NTAgVGQKKFByb2plY3QgU3BlY2lmaWNhdGlvbikgVGoKRVQKZW5kc3RyZWFtCmVuZG9iagp4cmVmCjAgNwowMDAwMDAwMDAwIDY1NTM1IGYgCjAwMDAwMDAwMDkgMDAwMDAgbiAKMDAwMDAwMDE1NyAwMDAwMCBuIAowMDAwMDAwMjA4IDAwMDAwIG4gCjAwMDAwMDAyNjMgMDAwMDAgbiAKMDAwMDAwMDM2NSAwMDAwMCBuIAowMDAwMDAwNDI2IDAwMDAwIG4gCnRyYWlsZXIKPDwKL1NpemUgNwovUm9vdCAyIDAgUgovSW5mbyAxIDAgUgo+PgpzdGFydHhyZWYKNTE4CiUlRU9G" class="w-full h-full rounded-lg border border-tertiary/50"></iframe>
            </div>
        </div>

        <!-- Right Column - File Details -->
        <div id="file-details-panel" class="w-1/3 bg-gray-800 flex flex-col overflow-hidden min-w-[320px] max-w-[480px]" style="resize: horizontal;">
            
            <!-- File Header -->
            <div class="p-4 border-b border-tertiary/50 flex items-center justify-between">
                <div class="flex items-center gap-2 flex-1 min-w-0">
                    <i class="text-secondary text-lg flex-shrink-0" data-fa-i2svg=""><svg class="svg-inline--fa fa-file-pdf" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="file-pdf" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M0 64C0 28.7 28.7 0 64 0H224V128c0 17.7 14.3 32 32 32H384V304H176c-35.3 0-64 28.7-64 64V512H64c-35.3 0-64-28.7-64-64V64zm384 64H256V0L384 128zM176 352h32c30.9 0 56 25.1 56 56s-25.1 56-56 56H192v32c0 8.8-7.2 16-16 16s-16-7.2-16-16V448 368c0-8.8 7.2-16 16-16zm32 80c13.3 0 24-10.7 24-24s-10.7-24-24-24H192v48h16zm96-80h32c26.5 0 48 21.5 48 48v64c0 26.5-21.5 48-48 48H304c-8.8 0-16-7.2-16-16V368c0-8.8 7.2-16 16-16zm32 128c8.8 0 16-7.2 16-16V400c0-8.8-7.2-16-16-16H320v96h16zm80-112c0-8.8 7.2-16 16-16h48c8.8 0 16 7.2 16 16s-7.2 16-16 16H448v32h32c8.8 0 16 7.2 16 16s-7.2 16-16 16H448v48c0 8.8-7.2 16-16 16s-16-7.2-16-16V432 368z"></path></svg></i>
                    <span class="text-supplement1 font-semibold text-sm truncate">project-spec.pdf</span>
                </div>
                <div class="flex items-center gap-1 ml-2">
                    <button class="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                        <i class="text-red-500 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-lock" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="lock" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M144 144v48H304V144c0-44.2-35.8-80-80-80s-80 35.8-80 80zM80 192V144C80 64.5 144.5 0 224 0s144 64.5 144 144v48h16c35.3 0 64 28.7 64 64V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V256c0-35.3 28.7-64 64-64H80z"></path></svg></i>
                        <div class="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                            Private Document
                        </div>
                    </button>
                    <button class="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                        <i class="text-gray-400 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-ellipsis-vertical" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="ellipsis-vertical" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 128 512" data-fa-i2svg=""><path fill="currentColor" d="M64 360a56 56 0 1 0 0 112 56 56 0 1 0 0-112zm0-160a56 56 0 1 0 0 112 56 56 0 1 0 0-112zM120 96A56 56 0 1 0 8 96a56 56 0 1 0 112 0z"></path></svg></i>
                        <div class="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                            System Operations
                        </div>
                    </button>
                    <button onclick="closePDFViewer()" class="p-1 hover:bg-gray-700 rounded transition-colors">
                        <i class="text-gray-400 text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-xmark" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="xmark" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"></path></svg></i>
                    </button>
                </div>
            </div>
            
            <!-- Tags Section -->
            <div class="p-3 border-b border-tertiary/50">
                <p class="text-xs text-gray-400 mb-2">Select any key ideas about this doc to enhance the AI context learning.</p>
                <div class="flex flex-wrap gap-1 mb-2">
                    <span class="px-2 py-0.5 bg-primary/20 text-primary text-xs rounded-full border border-primary/30 font-medium">UI Design</span>
                    <span class="px-2 py-0.5 bg-secondary/20 text-secondary text-xs rounded-full border border-secondary/30 font-medium">Specifications</span>
                    <span class="px-2 py-0.5 bg-supplement2/20 text-supplement2 text-xs rounded-full border border-supplement2/30 font-medium">Requirements</span>
                    <span class="px-2 py-0.5 bg-gray-700 text-gray-300 text-xs rounded-full">Components</span>
                    <span class="px-2 py-0.5 bg-gray-700 text-gray-300 text-xs rounded-full">Wireframes</span>
                    <span class="px-2 py-0.5 bg-gray-700 text-gray-300 text-xs rounded-full">Architecture</span>
                    <span class="px-2 py-0.5 bg-gray-700 text-gray-300 text-xs rounded-full">Testing</span>
                    <button class="px-2 py-0.5 border border-dashed border-gray-600 text-gray-400 text-xs rounded-full hover:border-gray-500 transition-colors">
                        <i class="mr-1" data-fa-i2svg=""><svg class="svg-inline--fa fa-plus" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="plus" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M256 80c0-17.7-14.3-32-32-32s-32 14.3-32 32V224H48c-17.7 0-32 14.3-32 32s14.3 32 32 32H192V432c0 17.7 14.3 32 32 32s32-14.3 32-32V288H400c17.7 0 32-14.3 32-32s-14.3-32-32-32H256V80z"></path></svg></i>Add
                    </button>
                </div>
            </div>
            
            <!-- Document Summary -->
            <div class="p-3 border-b border-tertiary/50 flex-1 overflow-y-auto">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-supplement1 font-semibold text-sm">Note #3</h3>
                    <div class="flex items-center gap-2">
                        <div class="flex items-center gap-1">
                            <button class="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                                <i class="text-supplement2 text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-pen-to-square" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="pen-to-square" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M471.6 21.7c-21.9-21.9-57.3-21.9-79.2 0L362.3 51.7l97.9 97.9 30.1-30.1c21.9-21.9 21.9-57.3 0-79.2L471.6 21.7zm-299.2 220c-6.1 6.1-10.8 13.6-13.5 21.9l-29.6 88.8c-2.9 8.6-.6 18.1 5.8 24.6s15.9 8.7 24.6 5.8l88.8-29.6c8.2-2.7 15.7-7.4 21.9-13.5L437.7 172.3 339.7 74.3 172.4 241.7zM96 64C43 64 0 107 0 160V416c0 53 43 96 96 96H352c53 0 96-43 96-96V320c0-17.7-14.3-32-32-32s-32 14.3-32 32v96c0 17.7-14.3 32-32 32H96c-17.7 0-32-14.3-32-32V160c0-17.7 14.3-32 32-32h96c17.7 0 32-14.3 32-32s-14.3-32-32-32H96z"></path></svg></i>
                                <div class="absolute bottom-6 left-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                                    Edit Tags
                                </div>
                            </button>
                            <button class="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                                <i class="text-primary text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-floppy-disk" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="floppy-disk" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M64 32C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64H384c35.3 0 64-28.7 64-64V173.3c0-17-6.7-33.3-18.7-45.3L352 50.7C340 38.7 323.7 32 306.7 32H64zm0 96c0-17.7 14.3-32 32-32H288c17.7 0 32 14.3 32 32v64c0 17.7-14.3 32-32 32H96c-17.7 0-32-14.3-32-32V128zM224 288a64 64 0 1 1 0 128 64 64 0 1 1 0-128z"></path></svg></i>
                                <div class="absolute bottom-6 left-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                                    Save Changes
                                </div>
                            </button>
                        </div>
                        <div class="flex items-center gap-1 text-xs text-gray-400">
                            <span class="">3 of 12</span>
                            <button class="p-0.5 hover:bg-gray-700 rounded transition-colors">
                                <i class="text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-left" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-left" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" data-fa-i2svg=""><path fill="currentColor" d="M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l192 192c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256 246.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-192 192z"></path></svg></i>
                            </button>
                            <button class="p-0.5 hover:bg-gray-700 rounded transition-colors">
                                <i class="text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-chevron-right" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-right" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" data-fa-i2svg=""><path fill="currentColor" d="M310.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L242.7 256 73.4 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z"></path></svg></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-900 rounded-lg p-3 border border-tertiary/50 mb-3">
                    <p class="text-gray-300 text-xs leading-relaxed mb-2"></p><div class="">Document Summary:<br><br></div>
                        This comprehensive project specification document outlines the complete design system requirements for the Chatlo application. It includes detailed UI components, color schemes, typography guidelines, and interaction patterns.
                    <p></p>
                    <p class="text-gray-300 text-xs leading-relaxed mb-2">
                        The document covers 47 pages of technical specifications including:
                    </p>
                    <ul class="text-gray-300 text-xs space-y-1 mb-2 pl-3">
                        <li class="flex items-start gap-1">
                            <span class="text-primary mt-0.5 text-xs">•</span>
                            <span class="">Component library specifications (buttons, forms, navigation)</span>
                        </li>
                        <li class="flex items-start gap-1">
                            <span class="text-secondary mt-0.5 text-xs">•</span>
                            <span class="">Color palette and theming guidelines</span>
                        </li>
                        <li class="flex items-start gap-1">
                            <span class="text-supplement2 mt-0.5 text-xs">•</span>
                            <span class="">Responsive design breakpoints and layouts</span>
                        </li>
                        <li class="flex items-start gap-1">
                            <span class="text-primary mt-0.5 text-xs">•</span>
                            <span class="">Accessibility requirements and testing protocols</span>
                        </li>
                    </ul>
                    <p class="text-gray-400 text-xs">
                        Last updated: January 15, 2024 • 2.1 MB • 47 pages
                    </p>
                </div>
                
                <!-- Example Prompts -->
                <div class="mb-3">
                    <h4 class="text-supplement1 font-medium text-xs mb-2">Example Annotation Prompts</h4>
                    <div class="space-y-1">
                        <button class="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                            "Summarize the second paragraph"
                        </button>
                        <button class="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                            "Extract all color codes and create a palette reference"
                        </button>
                        <button class="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                            "List all component specifications with their properties"
                        </button>
                        <button class="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                            "Identify potential implementation challenges and solutions"
                        </button>
                        <button class="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                            "Compare this with existing design systems and best practices"
                        </button>
                        <button class="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                            "Generate test cases for accessibility compliance"
                        </button>
                    </div>
                </div>
                
                <!-- Custom Prompt Input -->
                <div class="mb-3">
                    <textarea placeholder="Enter your custom annotation prompt here..." class="w-full bg-gray-900 border border-tertiary/50 rounded-lg p-2 text-xs text-gray-300 placeholder-gray-500 resize-none focus:outline-none focus:border-primary/50 transition-colors" rows="3"></textarea>
                </div>
            </div>
            
            <!-- Smart Annotation Button -->
            <div class="p-3 border-b border-tertiary/50">
                <button class="w-full bg-primary hover:bg-primary/80 text-gray-900 font-semibold py-2 px-3 rounded-lg transition-colors flex items-center justify-center gap-2">
                    <i class="text-sm" data-fa-i2svg=""><svg class="svg-inline--fa fa-wand-magic-sparkles" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="wand-magic-sparkles" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" data-fa-i2svg=""><path fill="currentColor" d="M234.7 42.7L197 56.8c-3 1.1-5 4-5 7.2s2 6.1 5 7.2l37.7 14.1L248.8 123c1.1 3 4 5 7.2 5s6.1-2 7.2-5l14.1-37.7L315 71.2c3-1.1 5-4 5-7.2s-2-6.1-5-7.2L277.3 42.7 263.2 5c-1.1-3-4-5-7.2-5s-6.1 2-7.2 5L234.7 42.7zM46.1 395.4c-18.7 18.7-18.7 49.1 0 67.9l34.6 34.6c18.7 18.7 49.1 18.7 67.9 0L529.9 116.5c18.7-18.7 18.7-49.1 0-67.9L495.3 14.1c-18.7-18.7-49.1-18.7-67.9 0L46.1 395.4zM484.6 82.6l-105 105-23.3-23.3 105-105 23.3 23.3zM7.5 117.2C3 118.9 0 123.2 0 128s3 9.1 7.5 10.8L64 160l21.2 56.5c1.7 4.5 6 7.5 10.8 7.5s9.1-3 10.8-7.5L128 160l56.5-21.2c4.5-1.7 7.5-6 7.5-10.8s-3-9.1-7.5-10.8L128 96 106.8 39.5C105.1 35 100.8 32 96 32s-9.1 3-10.8 7.5L64 96 7.5 117.2zm352 256c-4.5 1.7-7.5 6-7.5 10.8s3 9.1 7.5 10.8L416 416l21.2 56.5c1.7 4.5 6 7.5 10.8 7.5s9.1-3 10.8-7.5L480 416l56.5-21.2c4.5-1.7 7.5-6 7.5-10.8s-3-9.1-7.5-10.8L480 352l-21.2-56.5c-1.7-4.5-6-7.5-10.8-7.5s-9.1 3-10.8 7.5L416 352l-56.5 21.2z"></path></svg></i>
                    <span class="text-sm">Smart Annotation</span>
                </button>
                <p class="text-xs text-gray-400 mt-1 text-center">
                    One-click detail summarization and write your thoughts for context building
                </p>
            </div>
            
            <!-- Action Buttons -->
            <div class="p-3">
                <div class="grid grid-cols-2 gap-2">
                    <div class="flex flex-col">
                        <button class="bg-secondary/10 hover:bg-secondary/20 border border-secondary/30 text-secondary font-medium py-2 px-3 rounded-lg transition-colors text-xs flex items-center justify-center gap-1">
                            <i class="text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-comments" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="comments" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" data-fa-i2svg=""><path fill="currentColor" d="M208 352c114.9 0 208-78.8 208-176S322.9 0 208 0S0 78.8 0 176c0 38.6 14.7 74.3 39.6 103.4c-3.5 9.4-8.7 17.7-14.2 24.7c-4.8 6.2-9.7 11-13.3 14.3c-1.8 1.6-3.3 2.9-4.3 3.7c-.5 .4-.9 .7-1.1 .8l-.2 .2 0 0 0 0C1 327.2-1.4 334.4 .8 340.9S9.1 352 16 352c21.8 0 43.8-5.6 62.1-12.5c9.2-3.5 17.8-7.4 25.3-11.4C134.1 343.3 169.8 352 208 352zM448 176c0 112.3-99.1 196.9-216.5 207C255.8 457.4 336.4 512 432 512c38.2 0 73.9-8.7 104.7-23.9c7.5 4 16 7.9 25.2 11.4c18.3 6.9 40.3 12.5 62.1 12.5c6.9 0 13.1-4.5 15.2-11.1c2.1-6.6-.2-13.8-5.8-17.9l0 0 0 0-.2-.2c-.2-.2-.6-.4-1.1-.8c-1-.8-2.5-2-4.3-3.7c-3.6-3.3-8.5-8.1-13.3-14.3c-5.5-7-10.7-15.4-14.2-24.7c24.9-29 39.6-64.7 39.6-103.4c0-92.8-84.9-168.9-192.6-175.5c.4 5.1 .6 10.3 .6 15.5z"></path></svg></i>
                            <span class="">Ask AI</span>
                        </button>
                        <p class="text-xs text-gray-500 mt-1 text-center">Send to chat to explore more with other LLM models</p>
                    </div>
                    <div class="flex flex-col">
                        <button class="bg-supplement2/10 hover:bg-supplement2/20 border border-supplement2/30 text-supplement2 font-medium py-2 px-3 rounded-lg transition-colors text-xs flex items-center justify-center gap-1">
                            <i class="text-xs" data-fa-i2svg=""><svg class="svg-inline--fa fa-text-width" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="text-width" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M64 128V96H192l0 128H176c-17.7 0-32 14.3-32 32s14.3 32 32 32h96c17.7 0 32-14.3 32-32s-14.3-32-32-32H256l0-128H384v32c0 17.7 14.3 32 32 32s32-14.3 32-32V80c0-26.5-21.5-48-48-48H224 48C21.5 32 0 53.5 0 80v48c0 17.7 14.3 32 32 32s32-14.3 32-32zM9.4 361.4c-12.5 12.5-12.5 32.8 0 45.3l64 64c9.2 9.2 22.9 11.9 34.9 6.9s19.8-16.6 19.8-29.6V416H320v32c0 12.9 7.8 24.6 19.8 29.6s25.7 2.2 34.9-6.9l64-64c12.5-12.5 12.5-32.8 0-45.3l-64-64c-9.2-9.2-22.9-11.9-34.9-6.9s-19.8 16.6-19.8 29.6v32H128V320c0-12.9-7.8-24.6-19.8-29.6s-25.7-2.2-34.9 6.9l-64 64z"></path></svg></i>
                            <span class="">Extract Text</span>
                        </button>
                        <p class="text-xs text-gray-500 mt-1 text-center">OCR the text and copy to the clipboard</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
        </div>
    </div>

    <script>
        function toggleFolder(folderId) {
            // Add folder toggle functionality
        }
        
        function selectFile(fileName) {
            // Add file selection functionality
        }
    </script>

</body></html>

## Implementation Requirements

### HTML Structure Enhancement
The existing HTML layout provides the foundation, but requires these key additions for intelligence collection:

#### Smart Annotation Controls
```html
<!-- Add to file-details-panel header -->
<div class="intelligence-controls p-3 border-b border-tertiary/50">
  <button id="smart-annotation-btn" class="w-full bg-primary hover:bg-primary/80 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors mb-2">
    <i class="fas fa-brain mr-2"></i>Smart Annotation
  </button>
  <div class="flex gap-2">
    <button id="ai-chat-btn" class="flex-1 bg-secondary/20 hover:bg-secondary/30 text-secondary border border-secondary/30 py-1.5 px-3 rounded text-sm transition-colors">
      <i class="fas fa-comment mr-1"></i>AI Chat
    </button>
    <button id="extract-btn" class="flex-1 bg-supplement2/20 hover:bg-supplement2/30 text-supplement2 border border-supplement2/30 py-1.5 px-3 rounded text-sm transition-colors">
      <i class="fas fa-extract mr-1"></i>Extract
    </button>
  </div>
</div>
```

#### Entity Tags Enhancement
```html
<!-- Enhance existing tags section -->
<div class="entity-tags-section p-3 border-b border-tertiary/50">
  <p class="text-xs text-gray-400 mb-2">Select key ideas to enhance AI context learning</p>

  <!-- AI Suggested Tags -->
  <div class="suggested-tags mb-3" id="ai-suggested-tags">
    <h4 class="text-xs text-supplement2 mb-2 font-medium">AI Discovered Concepts:</h4>
    <div class="flex flex-wrap gap-1" id="suggested-tags-container">
      <!-- Dynamically populated by AI -->
    </div>
  </div>

  <!-- User Selected Tags -->
  <div class="selected-tags" id="user-selected-tags">
    <h4 class="text-xs text-primary mb-2 font-medium">Selected Entities:</h4>
    <div class="flex flex-wrap gap-1" id="selected-tags-container">
      <!-- User selections displayed here -->
    </div>
  </div>

  <!-- Confidence Indicator -->
  <div class="confidence-indicator mt-2">
    <div class="flex items-center gap-2 text-xs text-gray-400">
      <span>AI Confidence:</span>
      <div class="flex-1 bg-gray-700 rounded-full h-1">
        <div id="confidence-bar" class="bg-primary h-1 rounded-full transition-all" style="width: 0%"></div>
      </div>
      <span id="confidence-score">0%</span>
    </div>
  </div>
</div>
```

#### Annotation Display Area
```html
<!-- Enhanced annotation area -->
<div class="annotation-area flex-1 overflow-y-auto p-3">
  <div class="annotation-header flex items-center justify-between mb-3">
    <h3 class="text-supplement1 font-semibold text-sm">
      Intelligence Note #<span id="note-number">1</span>
    </h3>
    <div class="flex items-center gap-2">
      <div class="annotation-controls flex items-center gap-1">
        <button id="edit-annotation" class="p-1 hover:bg-gray-700 rounded transition-colors">
          <i class="fas fa-edit text-supplement2 text-xs"></i>
        </button>
        <button id="save-annotation" class="p-1 hover:bg-gray-700 rounded transition-colors">
          <i class="fas fa-save text-primary text-xs"></i>
        </button>
        <button id="export-annotation" class="p-1 hover:bg-gray-700 rounded transition-colors">
          <i class="fas fa-download text-gray-400 text-xs"></i>
        </button>
      </div>
      <div class="session-navigation flex items-center gap-1 text-xs text-gray-400">
        <span id="session-counter">1 of 1</span>
        <button id="prev-session" class="p-0.5 hover:bg-gray-700 rounded transition-colors">
          <i class="fas fa-chevron-left text-xs"></i>
        </button>
        <button id="next-session" class="p-0.5 hover:bg-gray-700 rounded transition-colors">
          <i class="fas fa-chevron-right text-xs"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- AI Insights Display -->
  <div class="annotation-content bg-gray-900 rounded-lg p-3 border border-tertiary/50">
    <div id="ai-insights-container">
      <div class="loading-state hidden" id="analysis-loading">
        <div class="flex items-center gap-2 text-gray-400 text-sm">
          <i class="fas fa-spinner fa-spin"></i>
          <span>Analyzing document...</span>
        </div>
      </div>

      <div class="insights-content" id="insights-display">
        <div class="empty-state text-center py-8 text-gray-500">
          <i class="fas fa-brain text-2xl mb-2"></i>
          <p class="text-sm">Click "Smart Annotation" to analyze this document</p>
        </div>
      </div>
    </div>

    <!-- User Notes Section -->
    <div class="user-notes mt-4" id="user-notes-section">
      <h4 class="text-xs text-gray-400 mb-2 font-medium">Your Notes:</h4>
      <textarea id="user-notes-input"
                class="w-full bg-gray-800 border border-gray-600 rounded p-2 text-sm text-gray-300 resize-none"
                rows="3"
                placeholder="Add your own insights..."></textarea>
    </div>
  </div>
</div>
```

### JavaScript Implementation

#### Core Intelligence System
```javascript
class DocumentIntelligenceSystem {
  constructor() {
    this.currentDocument = null;
    this.currentSession = null;
    this.aiModel = 'gemma3-32k';
    this.confidenceThreshold = 0.7;
  }

  async initializeDocument(documentPath) {
    this.currentDocument = {
      path: documentPath,
      name: path.basename(documentPath),
      type: path.extname(documentPath).slice(1),
      hash: await this.generateDocumentHash(documentPath),
      vault: this.getCurrentVault()
    };

    await this.loadExistingSessions();
    this.updateUI();
  }

  async triggerSmartAnnotation() {
    this.showLoadingState();

    try {
      const analysisResult = await this.analyzeDocument();
      this.currentSession = this.createSession(analysisResult);

      await this.saveSession();
      this.displayResults(analysisResult);
      this.recordInteraction('smart_annotation_triggered');

    } catch (error) {
      this.handleAnalysisError(error);
    } finally {
      this.hideLoadingState();
    }
  }

  async analyzeDocument() {
    const documentContent = await this.extractDocumentContent();

    const prompt = `
      Analyze this document and extract:
      1. Key entities and concepts
      2. Main topics and themes
      3. Technical terms and jargon
      4. Action items or requirements
      5. Document summary and importance

      Document: ${documentContent}

      Return structured JSON with entities, confidence scores, and insights.
    `;

    const response = await this.callAIModel(prompt);
    return this.parseAIResponse(response);
  }

  displayResults(analysisResult) {
    this.displaySuggestedTags(analysisResult.entities);
    this.displayInsights(analysisResult.insights);
    this.updateConfidenceIndicator(analysisResult.confidence);
    this.enableTagSelection();
  }

  displaySuggestedTags(entities) {
    const container = document.getElementById('suggested-tags-container');
    container.innerHTML = '';

    entities.forEach(entity => {
      const tagElement = this.createTagElement(entity);
      container.appendChild(tagElement);
    });
  }

  createTagElement(entity) {
    const tag = document.createElement('button');
    tag.className = `px-2 py-0.5 bg-gray-700 hover:bg-gray-600 text-gray-300 text-xs rounded-full border border-gray-600 transition-colors`;
    tag.innerHTML = `
      ${entity.name}
      <span class="ml-1 text-xs opacity-60">${Math.round(entity.confidence * 100)}%</span>
    `;

    tag.addEventListener('click', () => this.handleTagSelection(entity, tag));
    return tag;
  }

  handleTagSelection(entity, tagElement) {
    const isSelected = tagElement.classList.toggle('selected');

    if (isSelected) {
      tagElement.className = tagElement.className.replace('bg-gray-700', 'bg-primary/20');
      tagElement.className = tagElement.className.replace('border-gray-600', 'border-primary/30');
      tagElement.className = tagElement.className.replace('text-gray-300', 'text-primary');
      this.addSelectedTag(entity);
    } else {
      tagElement.className = tagElement.className.replace('bg-primary/20', 'bg-gray-700');
      tagElement.className = tagElement.className.replace('border-primary/30', 'border-gray-600');
      tagElement.className = tagElement.className.replace('text-primary', 'text-gray-300');
      this.removeSelectedTag(entity);
    }

    this.recordEntitySelection(entity, isSelected);
  }

  recordEntitySelection(entity, selected) {
    const interaction = {
      action: 'entity_selection',
      timestamp: new Date().toISOString(),
      entity: entity.name,
      confidence: entity.confidence,
      selected: selected,
      session_id: this.currentSession.id
    };

    this.currentSession.user_interactions.push(interaction);
    this.saveSession();
  }

  async saveSession() {
    const sessionPath = this.getSessionPath();
    await this.writeJSONFile(sessionPath, this.currentSession);
    await this.updateEntityIndex();
    await this.updateLearningPatterns();
  }

  getSessionPath() {
    const intelligenceDir = path.join(
      this.currentDocument.vault,
      '.intelligence',
      this.currentDocument.hash
    );

    this.ensureDirectoryExists(intelligenceDir);
    return path.join(intelligenceDir, `session_${Date.now()}.json`);
  }
}

// Initialize system
const intelligenceSystem = new DocumentIntelligenceSystem();

// Event Listeners
document.getElementById('smart-annotation-btn').addEventListener('click', () => {
  intelligenceSystem.triggerSmartAnnotation();
});

document.getElementById('ai-chat-btn').addEventListener('click', () => {
  const documentData = intelligenceSystem.getCurrentDocumentData();
  window.electronAPI.navigateToChat(documentData);
});

document.getElementById('extract-btn').addEventListener('click', () => {
  if (intelligenceSystem.isExtractionSupported()) {
    intelligenceSystem.extractDocumentData();
  } else {
    intelligenceSystem.showUnsupportedMessage();
  }
});
```

### Data Processing Pipeline

#### Entity Extraction Pipeline
```javascript
class EntityExtractionPipeline {
  constructor(aiModel) {
    this.model = aiModel;
    this.entityTypes = [
      'content_category',
      'technical_concept',
      'action_item',
      'relationship',
      'key_insight'
    ];
  }

  async processDocument(content) {
    const entities = await this.extractEntities(content);
    const relationships = await this.findRelationships(entities);
    const insights = await this.generateInsights(content, entities);

    return {
      entities: this.rankEntities(entities),
      relationships: relationships,
      insights: insights,
      confidence: this.calculateOverallConfidence(entities)
    };
  }

  async extractEntities(content) {
    const prompt = this.buildEntityExtractionPrompt(content);
    const response = await this.model.generate(prompt);
    return this.parseEntityResponse(response);
  }

  rankEntities(entities) {
    return entities
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 20); // Limit to top 20 entities
  }
}
```

### Performance Optimization

#### Caching Strategy
```javascript
class IntelligenceCache {
  constructor() {
    this.cache = new Map();
    this.maxCacheSize = 100;
    this.cacheTimeout = 24 * 60 * 60 * 1000; // 24 hours
  }

  async getCachedAnalysis(documentHash) {
    const cached = this.cache.get(documentHash);
    if (cached && !this.isExpired(cached)) {
      return cached.data;
    }
    return null;
  }

  setCachedAnalysis(documentHash, data) {
    if (this.cache.size >= this.maxCacheSize) {
      this.evictOldest();
    }

    this.cache.set(documentHash, {
      data: data,
      timestamp: Date.now()
    });
  }
}
```

This comprehensive specification provides the complete framework for implementing ChatLo's intelligent document analysis system, focusing on maximum data collection for enhanced AI assistance while maintaining user control and privacy through local processing.
