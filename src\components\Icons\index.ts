// Centralized FontAwesome Icon Registry
// Rule 2.2: All icons imported from registry only, offline accessible
// Icons sourced from local fontawesome-free-7.0.0-web folder

import { IconDefinition } from '@fortawesome/fontawesome-svg-core'

// Import all required icons from local FontAwesome packages
import {
  faHome,
  faComment,
  faClockRotateLeft,
  faFolderTree,
  faUser,
  faGear,
  faChevronRight,
  faFile,
  faFileText,
  faFileCode,
  faFileImage,
  faFilePdf,
  faLayerGroup,
  faArrowsRotate,
  faUpload,
  faXmark,
  faRefresh,
  faFolder,
  faSearch,
  faGrip,
  faList,
  faSort,
  faCircle,
  faWifi,
  faBan,
  faInfo,
  faCheck,
  faBrain,
  faPlay,
  faChartLine,
  faSpinner,
  faCheckCircle,
  faExclamationTriangle,
  faCog,
  faStar,
  faClock,
  faPlus,
  faKeyboard,
  faTimes,
  faBolt,
  faFileWord,
  faFileExcel,
  faFilePowerpoint,
  faFileVideo,
  faFileAudio,
  faFileZipper,
  faSitemap,
  faThLarge,
  faSortUp,
  faSortDown,
  faFileLines,
  faChevronDown,
  faPaperclip,
  faArrowLeft,
  faDownload,
  faTrash,
  faPaperPlane,
  faUsers,
  faLightbulb,
  faArrowRight,
  faEdit,
  faTags,
  faCube,
  faChevronLeft,
  faWandMagic,
  faBookmark,
  faSquareCheck,
  faSquare,
  faChevronUp,
  faFolderPlus,
  faFilter,
  faEye,
  faExternalLinkAlt,
  faFolderOpen,
  faClone,
  faLanguage,
  faComments,
  faMinus,
  faLink,
  faShare,
  faQrcode,
  faClipboard,
  faHand,
  faExpand,
  faCompress,
  faCut,
  faCopy,
  faSearchMinus,
  faSearchPlus,
  faLock,
  faEllipsisVertical,
  faPenToSquare,
  faFloppyDisk,
  faTextWidth
} from '@fortawesome/free-solid-svg-icons'

// Centralized icon registry - single source of truth
export const ICONS = {
  // Navigation Icons
  home: faHome,
  comment: faComment,
  history: faClockRotateLeft,
  files: faFolderTree,
  user: faUser,
  settings: faGear,
  gear: faGear,
  
  // UI Icons
  chevronRight: faChevronRight,
  chevronDown: faChevronDown,
  file: faFile,
  fileText: faFileText,
  fileCode: faFileCode,
  fileImage: faFileImage,
  filePdf: faFilePdf,
  fileWord: faFileWord,
  fileExcel: faFileExcel,
  filePowerpoint: faFilePowerpoint,
  fileVideo: faFileVideo,
  fileAudio: faFileAudio,
  fileZipper: faFileZipper,
  fileLines: faFileLines,
  
  // Action Icons
  layerGroup: faLayerGroup,
  arrowsRotate: faArrowsRotate,
  upload: faUpload,
  xmark: faXmark,
  refresh: faRefresh,
  folder: faFolder,
  search: faSearch,
  grip: faGrip,
  list: faList,
  sort: faSort,
  sortUp: faSortUp,
  sortDown: faSortDown,
  
  // Status Icons
  circle: faCircle,
  wifi: faWifi,
  ban: faBan,
  info: faInfo,
  check: faCheck,
  
  // Intelligence Icons
  brain: faBrain,
  play: faPlay,
  chartLine: faChartLine,
  spinner: faSpinner,
  checkCircle: faCheckCircle,
  exclamationTriangle: faExclamationTriangle,
  cog: faCog,
  star: faStar,
  
  // Time Icons
  clock: faClock,
  
  // Quick Actions
  plus: faPlus,
  keyboard: faKeyboard,
  times: faTimes,
  lightning: faBolt,
  paperclip: faPaperclip,
  arrowLeft: faArrowLeft,
  download: faDownload,
  trash: faTrash,
  paperPlane: faPaperPlane,
  users: faUsers,
  lightbulb: faLightbulb,
  arrowRight: faArrowRight,
  edit: faEdit,
  tags: faTags,
  cube: faCube,
  chevronLeft: faChevronLeft,
  magic: faWandMagic,
  bookmark: faBookmark,
  checkSquare: faSquareCheck,
  square: faSquare,
  chevronUp: faChevronUp,
  folderPlus: faFolderPlus,
  filter: faFilter,
  eye: faEye,
  externalLink: faExternalLinkAlt,
  folderOpen: faFolderOpen,
  clone: faClone,
  language: faLanguage,
  comments: faComments,
  minus: faMinus,
  link: faLink,
  share: faShare,
  qrcode: faQrcode,
  clipboard: faClipboard,
  hand: faHand,
  expand: faExpand,
  compress: faCompress,
  cut: faCut,
  copy: faCopy,
  searchMinus: faSearchMinus,
  searchPlus: faSearchPlus,
  lock: faLock,
  ellipsisVertical: faEllipsisVertical,
  penToSquare: faPenToSquare,
  floppyDisk: faFloppyDisk,
  textWidth: faTextWidth,
  wandMagicSparkles: faWandMagic,

  // File Explorer
  sitemap: faSitemap,
  thLarge: faThLarge
} as const

// Type for icon names
export type IconName = keyof typeof ICONS

// Helper function to get icon by name
export const getIcon = (name: IconName): IconDefinition => {
  return ICONS[name]
}

// File type icon mapping with colors
export const getFileTypeIcon = (fileName: string): { icon: IconDefinition; color: string } => {
  const extension = fileName.split('.').pop()?.toLowerCase() || ''
  
  const fileTypeMap: Record<string, { icon: IconDefinition; color: string }> = {
    // Text files
    'txt': { icon: ICONS.fileText, color: 'text-gray-400' },
    'md': { icon: ICONS.fileText, color: 'text-blue-400' },
    'readme': { icon: ICONS.fileText, color: 'text-blue-400' },
    
    // Code files
    'js': { icon: ICONS.fileCode, color: 'text-yellow-400' },
    'ts': { icon: ICONS.fileCode, color: 'text-blue-500' },
    'tsx': { icon: ICONS.fileCode, color: 'text-blue-500' },
    'jsx': { icon: ICONS.fileCode, color: 'text-cyan-400' },
    'html': { icon: ICONS.fileCode, color: 'text-orange-400' },
    'css': { icon: ICONS.fileCode, color: 'text-blue-400' },
    'scss': { icon: ICONS.fileCode, color: 'text-pink-400' },
    'json': { icon: ICONS.fileCode, color: 'text-green-400' },
    'xml': { icon: ICONS.fileCode, color: 'text-orange-400' },
    'py': { icon: ICONS.fileCode, color: 'text-green-500' },
    'java': { icon: ICONS.fileCode, color: 'text-red-500' },
    'cpp': { icon: ICONS.fileCode, color: 'text-blue-600' },
    'c': { icon: ICONS.fileCode, color: 'text-blue-600' },
    
    // Images
    'jpg': { icon: ICONS.fileImage, color: 'text-green-400' },
    'jpeg': { icon: ICONS.fileImage, color: 'text-green-400' },
    'png': { icon: ICONS.fileImage, color: 'text-green-400' },
    'gif': { icon: ICONS.fileImage, color: 'text-green-400' },
    'svg': { icon: ICONS.fileImage, color: 'text-purple-400' },
    'webp': { icon: ICONS.fileImage, color: 'text-green-400' },
    
    // Documents
    'pdf': { icon: ICONS.filePdf, color: 'text-red-400' },
    'doc': { icon: ICONS.fileWord, color: 'text-blue-500' },
    'docx': { icon: ICONS.fileWord, color: 'text-blue-500' },
    'xls': { icon: ICONS.fileExcel, color: 'text-green-500' },
    'xlsx': { icon: ICONS.fileExcel, color: 'text-green-500' },
    'ppt': { icon: ICONS.filePowerpoint, color: 'text-orange-500' },
    'pptx': { icon: ICONS.filePowerpoint, color: 'text-orange-500' },
    
    // Media
    'mp4': { icon: ICONS.fileVideo, color: 'text-purple-400' },
    'avi': { icon: ICONS.fileVideo, color: 'text-purple-400' },
    'mov': { icon: ICONS.fileVideo, color: 'text-purple-400' },
    'mp3': { icon: ICONS.fileAudio, color: 'text-pink-400' },
    'wav': { icon: ICONS.fileAudio, color: 'text-pink-400' },
    'flac': { icon: ICONS.fileAudio, color: 'text-pink-400' },
    
    // Archives
    'zip': { icon: ICONS.fileZipper, color: 'text-yellow-600' },
    'rar': { icon: ICONS.fileZipper, color: 'text-yellow-600' },
    '7z': { icon: ICONS.fileZipper, color: 'text-yellow-600' },
    'tar': { icon: ICONS.fileZipper, color: 'text-yellow-600' },
    'gz': { icon: ICONS.fileZipper, color: 'text-yellow-600' }
  }
  
  return fileTypeMap[extension] || { icon: ICONS.file, color: 'text-gray-400' }
}

// Icon name mapping for breadcrumb navigation
export const getIconForName = (iconName: string): IconDefinition => {
  const iconMap: Record<string, IconDefinition> = {
    'home': ICONS.home,
    'comment': ICONS.comment,
    'folder': ICONS.folder,
    'clock': ICONS.clock,
    'gear': ICONS.settings,
    'file': ICONS.file,
    'fileText': ICONS.fileText,
    'fileCode': ICONS.fileCode,
    'fileImage': ICONS.fileImage,
    'filePdf': ICONS.filePdf
  }
  
  return iconMap[iconName] || ICONS.file
}

// Quick action icon mapping
export const getIconForAction = (iconName: string): IconDefinition => {
  const actionMap: Record<string, IconDefinition> = {
    'search': ICONS.search,
    'comment': ICONS.comment,
    'upload': ICONS.upload,
    'clock': ICONS.clock,
    'plus': ICONS.plus,
    'folder': ICONS.folder,
    'gear': ICONS.settings,
    'keyboard': ICONS.keyboard,
    'times': ICONS.times,
    'lightning': ICONS.lightning
  }
  
  return actionMap[iconName] || ICONS.file
}
