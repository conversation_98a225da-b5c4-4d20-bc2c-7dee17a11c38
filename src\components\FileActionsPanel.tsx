import React, { useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { SmartAnnotationPanel } from './SmartAnnotationPanel'

interface FileActionsPanelProps {
  selectedFile?: string
  filePath?: string
  fileContent?: string
  selectedContextId?: string
  onAction?: (action: string, data?: any) => void
}

interface ActionGroup {
  id: string
  title: string
  icon: any
  actions: Action[]
}

interface Action {
  id: string
  label: string
  icon: any
  description?: string
  disabled?: boolean
  destructive?: boolean
  onClick: () => void
}

export const FileActionsPanel: React.FC<FileActionsPanelProps> = ({
  selectedFile,
  filePath,
  fileContent,
  selectedContextId,
  onAction
}) => {
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set(['system', 'ai']))
  const [showSmartAnnotation, setShowSmartAnnotation] = useState(false)

  const toggleGroup = (groupId: string) => {
    const newExpanded = new Set(expandedGroups)
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId)
    } else {
      newExpanded.add(groupId)
    }
    setExpandedGroups(newExpanded)
  }

  const handleAction = (actionId: string, data?: any) => {
    console.log(`Action triggered: ${actionId}`, data)
    onAction?.(actionId, data)
  }

  const actionGroups: ActionGroup[] = [
    {
      id: 'system',
      title: 'System Operations',
      icon: ICONS.cog,
      actions: [
        {
          id: 'open-external',
          label: 'Open in System App',
          icon: ICONS.externalLink,
          description: 'Open with default system application',
          disabled: !selectedFile,
          onClick: () => handleAction('open-external', { file: selectedFile, path: filePath })
        },
        {
          id: 'show-in-explorer',
          label: 'Show in Explorer',
          icon: ICONS.folderOpen,
          description: 'Reveal file in system file explorer',
          disabled: !selectedFile,
          onClick: () => handleAction('show-in-explorer', { file: selectedFile, path: filePath })
        },
        {
          id: 'copy-path',
          label: 'Copy Path',
          icon: ICONS.copy,
          description: 'Copy file path to clipboard',
          disabled: !selectedFile,
          onClick: () => handleAction('copy-path', { file: selectedFile, path: filePath })
        },
        {
          id: 'rename',
          label: 'Rename',
          icon: ICONS.edit,
          description: 'Rename the selected file',
          disabled: !selectedFile,
          onClick: () => handleAction('rename', { file: selectedFile, path: filePath })
        },
        {
          id: 'duplicate',
          label: 'Duplicate',
          icon: ICONS.clone,
          description: 'Create a copy of the file',
          disabled: !selectedFile,
          onClick: () => handleAction('duplicate', { file: selectedFile, path: filePath })
        },
        {
          id: 'delete',
          label: 'Delete',
          icon: ICONS.trash,
          description: 'Move file to trash',
          disabled: !selectedFile,
          destructive: true,
          onClick: () => handleAction('delete', { file: selectedFile, path: filePath })
        }
      ]
    },
    {
      id: 'ai',
      title: 'AI Operations',
      icon: ICONS.brain,
      actions: [
        {
          id: 'summarize',
          label: 'Summarize Content',
          icon: ICONS.fileText,
          description: 'Generate AI summary of file content',
          disabled: !selectedFile,
          onClick: () => handleAction('summarize', { file: selectedFile, path: filePath })
        },
        {
          id: 'smart-annotation',
          label: 'Smart Annotation',
          icon: ICONS.magic,
          description: 'AI-powered entity extraction and insights',
          disabled: !selectedFile || !fileContent,
          onClick: () => setShowSmartAnnotation(!showSmartAnnotation)
        },
        {
          id: 'extract-keywords',
          label: 'Extract Keywords',
          icon: ICONS.tags,
          description: 'Extract key terms and topics',
          disabled: !selectedFile,
          onClick: () => handleAction('extract-keywords', { file: selectedFile, path: filePath })
        },
        {
          id: 'analyze-sentiment',
          label: 'Analyze Sentiment',
          icon: ICONS.chartLine,
          description: 'Analyze emotional tone of content',
          disabled: !selectedFile,
          onClick: () => handleAction('analyze-sentiment', { file: selectedFile, path: filePath })
        },
        {
          id: 'translate',
          label: 'Translate',
          icon: ICONS.language,
          description: 'Translate content to another language',
          disabled: !selectedFile,
          onClick: () => handleAction('translate', { file: selectedFile, path: filePath })
        },
        {
          id: 'chat-with-file',
          label: 'Chat with File',
          icon: ICONS.comments,
          description: 'Start AI conversation about this file',
          disabled: !selectedFile,
          onClick: () => handleAction('chat-with-file', { file: selectedFile, path: filePath })
        }
      ]
    },
    {
      id: 'vault',
      title: 'Vault Operations',
      icon: ICONS.cube,
      actions: [
        {
          id: 'add-to-context',
          label: 'Add to Context',
          icon: ICONS.plus,
          description: 'Add file to current context vault',
          disabled: !selectedFile || !selectedContextId,
          onClick: () => handleAction('add-to-context', { file: selectedFile, path: filePath, contextId: selectedContextId })
        },
        {
          id: 'remove-from-context',
          label: 'Remove from Context',
          icon: ICONS.minus,
          description: 'Remove file from current context vault',
          disabled: !selectedFile || !selectedContextId,
          onClick: () => handleAction('remove-from-context', { file: selectedFile, path: filePath, contextId: selectedContextId })
        },
        {
          id: 'move-to-vault',
          label: 'Move to Vault',
          icon: ICONS.arrowRight,
          description: 'Move file to another vault',
          disabled: !selectedFile,
          onClick: () => handleAction('move-to-vault', { file: selectedFile, path: filePath })
        },
        {
          id: 'copy-to-vault',
          label: 'Copy to Vault',
          icon: ICONS.copy,
          description: 'Copy file to another vault',
          disabled: !selectedFile,
          onClick: () => handleAction('copy-to-vault', { file: selectedFile, path: filePath })
        },
        {
          id: 'create-link',
          label: 'Create Link',
          icon: ICONS.link,
          description: 'Create symbolic link to file',
          disabled: !selectedFile,
          onClick: () => handleAction('create-link', { file: selectedFile, path: filePath })
        }
      ]
    },
    {
      id: 'sharing',
      title: 'Sharing & Export',
      icon: ICONS.share,
      actions: [
        {
          id: 'export-pdf',
          label: 'Export as PDF',
          icon: ICONS.filePdf,
          description: 'Convert and export as PDF',
          disabled: !selectedFile,
          onClick: () => handleAction('export-pdf', { file: selectedFile, path: filePath })
        },
        {
          id: 'create-archive',
          label: 'Create Archive',
          icon: ICONS.fileZipper,
          description: 'Create ZIP archive of file',
          disabled: !selectedFile,
          onClick: () => handleAction('create-archive', { file: selectedFile, path: filePath })
        },
        {
          id: 'generate-qr',
          label: 'Generate QR Code',
          icon: ICONS.qrcode,
          description: 'Generate QR code for file sharing',
          disabled: !selectedFile,
          onClick: () => handleAction('generate-qr', { file: selectedFile, path: filePath })
        },
        {
          id: 'copy-content',
          label: 'Copy Content',
          icon: ICONS.clipboard,
          description: 'Copy file content to clipboard',
          disabled: !selectedFile,
          onClick: () => handleAction('copy-content', { file: selectedFile, path: filePath })
        }
      ]
    }
  ]

  return (
    <div className="h-full bg-gray-900 border border-gray-700 rounded-lg overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center gap-2">
          <FontAwesomeIcon icon={ICONS.cog} className="text-primary text-sm" />
          <span className="text-sm font-medium text-supplement1">File Actions</span>
        </div>
        {selectedFile && (
          <div className="text-xs text-gray-400 truncate max-w-32" title={selectedFile}>
            {selectedFile}
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-2">
        {!selectedFile ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <FontAwesomeIcon icon={ICONS.hand} className="text-gray-400 text-2xl mb-2" />
              <p className="text-sm text-gray-400">Select a file to see actions</p>
            </div>
          </div>
        ) : showSmartAnnotation ? (
          /* Smart Annotation Panel */
          <div className="h-full">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-supplement1">Smart Annotation</h3>
              <button
                onClick={() => setShowSmartAnnotation(false)}
                className="text-gray-400 hover:text-supplement1 transition-colors"
                title="Back to Actions"
              >
                <FontAwesomeIcon icon={ICONS.times} className="text-sm" />
              </button>
            </div>
            <SmartAnnotationPanel
              selectedFile={selectedFile}
              filePath={filePath}
              fileContent={fileContent}
              selectedContextId={selectedContextId}
              onAnnotationComplete={(analysis) => {
                console.log('Annotation completed:', analysis)
                onAction?.('annotation-complete', { analysis })
              }}
              onEntitySelect={(entities) => {
                console.log('Entities selected:', entities)
                onAction?.('entities-selected', { entities })
              }}
            />
          </div>
        ) : (
          <div className="space-y-2">
            {actionGroups.map(group => (
              <div key={group.id} className="bg-gray-800 rounded-lg overflow-hidden">
                {/* Group Header */}
                <button
                  onClick={() => toggleGroup(group.id)}
                  className="w-full flex items-center justify-between p-3 hover:bg-gray-700 transition-colors"
                >
                  <div className="flex items-center gap-2">
                    <FontAwesomeIcon icon={group.icon} className="text-primary text-sm" />
                    <span className="text-sm font-medium text-supplement1">{group.title}</span>
                  </div>
                  <FontAwesomeIcon 
                    icon={expandedGroups.has(group.id) ? ICONS.chevronUp : ICONS.chevronDown} 
                    className="text-gray-400 text-xs" 
                  />
                </button>

                {/* Group Actions */}
                {expandedGroups.has(group.id) && (
                  <div className="border-t border-gray-700">
                    {group.actions.map(action => (
                      <button
                        key={action.id}
                        onClick={action.onClick}
                        disabled={action.disabled}
                        className={`
                          w-full flex items-center gap-3 p-3 text-left transition-colors
                          ${action.disabled 
                            ? 'text-gray-500 cursor-not-allowed' 
                            : action.destructive
                              ? 'text-secondary hover:bg-red-900/20'
                              : 'text-supplement1 hover:bg-gray-700'
                          }
                        `}
                        title={action.description}
                      >
                        <FontAwesomeIcon 
                          icon={action.icon} 
                          className={`text-sm ${action.destructive ? 'text-secondary' : 'text-primary'}`} 
                        />
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium">{action.label}</div>
                          {action.description && (
                            <div className="text-xs text-gray-400 truncate">{action.description}</div>
                          )}
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
