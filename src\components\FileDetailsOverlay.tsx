import React, { useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'

interface FileDetailsOverlayProps {
  filePath?: string
  onClose: () => void
}

export const FileDetailsOverlay: React.FC<FileDetailsOverlayProps> = ({
  filePath,
  onClose
}) => {
  const [customPrompt, setCustomPrompt] = useState('')

  if (!filePath) return null

  // Extract file name from path
  const fileName = filePath.split('/').pop() || filePath
  const fileExtension = fileName.split('.').pop()?.toLowerCase() || ''

  const handleSmartAnnotation = () => {
    // Smart annotation functionality will be implemented
    console.log('Smart Annotation triggered for:', fileName)
  }

  const handleAskAI = () => {
    // Navigate to chat with document
    console.log('Ask AI triggered for:', fileName)
  }

  const handleExtractText = () => {
    // Extract text functionality
    console.log('Extract Text triggered for:', fileName)
  }

  // Get appropriate icon based on file extension
  const getFileIcon = () => {
    switch (fileExtension) {
      case 'pdf':
        return ICONS.filePdf
      case 'md':
      case 'txt':
        return ICONS.fileText
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'svg':
        return ICONS.fileImage
      case 'js':
      case 'ts':
      case 'jsx':
      case 'tsx':
        return ICONS.fileCode
      default:
        return ICONS.file
    }
  }

  return (
    <div className="absolute inset-0 bg-gray-900 z-50 flex">
      {/* PDF Viewer Panel */}
      <div className="flex-1 bg-gray-800 flex flex-col border-r border-tertiary/50">
        {/* PDF Viewer Header */}
        <div className="p-4 border-b border-tertiary/50 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <button onClick={onClose} className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.arrowLeft} className="text-gray-400 text-sm" />
            </button>
            <FontAwesomeIcon icon={getFileIcon()} className="text-secondary text-lg" />
            <span className="text-supplement1 font-semibold text-lg">{fileName}</span>
          </div>
          <div className="flex items-center gap-2">
            <button className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.searchMinus} className="text-gray-400 text-sm" />
            </button>
            <span className="text-gray-400 text-sm">100%</span>
            <button className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.searchPlus} className="text-gray-400 text-sm" />
            </button>
            <button className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.download} className="text-gray-400 text-sm" />
            </button>
          </div>
        </div>
        
        {/* File Content */}
        <div className="flex-1 overflow-y-auto bg-gray-900 p-4">
          <div className="w-full h-full rounded-lg border border-tertiary/50 bg-white flex items-center justify-center">
            <div className="text-center text-gray-600">
              <FontAwesomeIcon icon={getFileIcon()} className="text-4xl mb-4" />
              <p className="text-lg font-medium">{fileName}</p>
              <p className="text-sm">Document Preview</p>
            </div>
          </div>
        </div>
      </div>

      {/* File Details Panel */}
      <div className="w-1/3 bg-gray-800 flex flex-col overflow-hidden min-w-[320px] max-w-[480px]">
        {/* File Header */}
        <div className="p-4 border-b border-tertiary/50 flex items-center justify-between">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <FontAwesomeIcon icon={getFileIcon()} className="text-secondary text-lg flex-shrink-0" />
            <span className="text-supplement1 font-semibold text-sm truncate">{fileName}</span>
          </div>
          <div className="flex items-center gap-1 ml-2">
            <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
              <FontAwesomeIcon icon={ICONS.lock} className="text-red-500 text-sm" />
              <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                Private Document
              </div>
            </button>
            <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
              <FontAwesomeIcon icon={ICONS.ellipsisVertical} className="text-gray-400 text-sm" />
              <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                System Operations
              </div>
            </button>
            <button onClick={onClose} className="p-1 hover:bg-gray-700 rounded transition-colors">
              <FontAwesomeIcon icon={ICONS.xmark} className="text-gray-400 text-sm" />
            </button>
          </div>
        </div>
        
        {/* Tags Section */}
        <div className="p-3 border-b border-tertiary/50">
          <p className="text-xs text-gray-400 mb-2">Select any key ideas about this doc to enhance the AI context learning.</p>
          <div className="flex flex-wrap gap-1 mb-2">
            <span className="px-2 py-0.5 bg-primary/20 text-primary text-xs rounded-full border border-primary/30 font-medium">UI Design</span>
            <span className="px-2 py-0.5 bg-secondary/20 text-secondary text-xs rounded-full border border-secondary/30 font-medium">Specifications</span>
            <span className="px-2 py-0.5 bg-supplement2/20 text-supplement2 text-xs rounded-full border border-supplement2/30 font-medium">Requirements</span>
            <span className="px-2 py-0.5 bg-gray-700 text-gray-300 text-xs rounded-full">Components</span>
            <span className="px-2 py-0.5 bg-gray-700 text-gray-300 text-xs rounded-full">Wireframes</span>
            <span className="px-2 py-0.5 bg-gray-700 text-gray-300 text-xs rounded-full">Architecture</span>
            <span className="px-2 py-0.5 bg-gray-700 text-gray-300 text-xs rounded-full">Testing</span>
            <button className="px-2 py-0.5 border border-dashed border-gray-600 text-gray-400 text-xs rounded-full hover:border-gray-500 transition-colors">
              <FontAwesomeIcon icon={ICONS.plus} className="mr-1" />Add
            </button>
          </div>
        </div>
        
        {/* Document Summary */}
        <div className="p-3 border-b border-tertiary/50 flex-1 overflow-y-auto">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-supplement1 font-semibold text-sm">Note #3</h3>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                  <FontAwesomeIcon icon={ICONS.penToSquare} className="text-supplement2 text-xs" />
                  <div className="absolute bottom-6 left-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                    Edit Tags
                  </div>
                </button>
                <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                  <FontAwesomeIcon icon={ICONS.floppyDisk} className="text-primary text-xs" />
                  <div className="absolute bottom-6 left-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                    Save Changes
                  </div>
                </button>
              </div>
              <div className="flex items-center gap-1 text-xs text-gray-400">
                <span>3 of 12</span>
                <button className="p-0.5 hover:bg-gray-700 rounded transition-colors">
                  <FontAwesomeIcon icon={ICONS.chevronLeft} className="text-xs" />
                </button>
                <button className="p-0.5 hover:bg-gray-700 rounded transition-colors">
                  <FontAwesomeIcon icon={ICONS.chevronRight} className="text-xs" />
                </button>
              </div>
            </div>
          </div>
          <div className="bg-gray-900 rounded-lg p-3 border border-tertiary/50 mb-3">
            <div className="mb-2">
              <strong>Document Summary:</strong>
              <br /><br />
            </div>
            <p className="text-gray-300 text-xs leading-relaxed mb-2">
              This comprehensive project specification document outlines the complete design system requirements for the Chatlo application. It includes detailed UI components, color schemes, typography guidelines, and interaction patterns.
            </p>
            <p className="text-gray-300 text-xs leading-relaxed mb-2">
              The document covers 47 pages of technical specifications including:
            </p>
            <ul className="text-gray-300 text-xs space-y-1 mb-2 pl-3">
              <li className="flex items-start gap-1">
                <span className="text-primary mt-0.5 text-xs">•</span>
                <span>Component library specifications (buttons, forms, navigation)</span>
              </li>
              <li className="flex items-start gap-1">
                <span className="text-secondary mt-0.5 text-xs">•</span>
                <span>Color palette and theming guidelines</span>
              </li>
              <li className="flex items-start gap-1">
                <span className="text-supplement2 mt-0.5 text-xs">•</span>
                <span>Responsive design breakpoints and layouts</span>
              </li>
              <li className="flex items-start gap-1">
                <span className="text-primary mt-0.5 text-xs">•</span>
                <span>Accessibility requirements and testing protocols</span>
              </li>
            </ul>
            <p className="text-gray-400 text-xs">
              Last updated: January 15, 2024 • 2.1 MB • 47 pages
            </p>
          </div>
          
          {/* Example Prompts */}
          <div className="mb-3">
            <h4 className="text-supplement1 font-medium text-xs mb-2">Example Annotation Prompts</h4>
            <div className="space-y-1">
              <button className="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                "Summarize the second paragraph"
              </button>
              <button className="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                "Extract all color codes and create a palette reference"
              </button>
              <button className="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                "List all component specifications with their properties"
              </button>
              <button className="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                "Identify potential implementation challenges and solutions"
              </button>
              <button className="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                "Compare this with existing design systems and best practices"
              </button>
              <button className="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                "Generate test cases for accessibility compliance"
              </button>
            </div>
          </div>
          
          {/* Custom Prompt Input */}
          <div className="mb-3">
            <textarea 
              placeholder="Enter your custom annotation prompt here..." 
              className="w-full bg-gray-900 border border-tertiary/50 rounded-lg p-2 text-xs text-gray-300 placeholder-gray-500 resize-none focus:outline-none focus:border-primary/50 transition-colors" 
              rows={3}
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
            />
          </div>
        </div>
        
        {/* Smart Annotation Button */}
        <div className="p-3 border-b border-tertiary/50">
          <button 
            onClick={handleSmartAnnotation}
            className="w-full bg-primary hover:bg-primary/80 text-gray-900 font-semibold py-2 px-3 rounded-lg transition-colors flex items-center justify-center gap-2"
          >
            <FontAwesomeIcon icon={ICONS.wandMagicSparkles} className="text-sm" />
            <span className="text-sm">Smart Annotation</span>
          </button>
          <p className="text-xs text-gray-400 mt-1 text-center">
            One-click detail summarization and write your thoughts for context building
          </p>
        </div>
        
        {/* Action Buttons */}
        <div className="p-3">
          <div className="grid grid-cols-2 gap-2">
            <div className="flex flex-col">
              <button 
                onClick={handleAskAI}
                className="bg-secondary/10 hover:bg-secondary/20 border border-secondary/30 text-secondary font-medium py-2 px-3 rounded-lg transition-colors text-xs flex items-center justify-center gap-1"
              >
                <FontAwesomeIcon icon={ICONS.comments} className="text-xs" />
                <span>Ask AI</span>
              </button>
              <p className="text-xs text-gray-500 mt-1 text-center">Send to chat to explore more with other LLM models</p>
            </div>
            <div className="flex flex-col">
              <button 
                onClick={handleExtractText}
                className="bg-supplement2/10 hover:bg-supplement2/20 border border-supplement2/30 text-supplement2 font-medium py-2 px-3 rounded-lg transition-colors text-xs flex items-center justify-center gap-1"
              >
                <FontAwesomeIcon icon={ICONS.textWidth} className="text-xs" />
                <span>Extract Text</span>
              </button>
              <p className="text-xs text-gray-500 mt-1 text-center">OCR the text and copy to the clipboard</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
