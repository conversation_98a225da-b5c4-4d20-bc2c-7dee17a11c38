---
type: "always_apply"
description: "Comprehensive Development Standards"
---
# ChatLo Development Standards - ACTIVE RULES
**Generated from**: Code Quality Review Meeting - 2025-07-20
**Status**: ✅ APPROVED AND ACTIVE
**Approved by**: Owner - 2025-07-23
**Priority**: Critical - Prevents recurring issues identified in Phase 1

## Owner's Core Principles (Priority Hierarchy)
1. **User Value First** - Every feature must serve clear user needs
2. **User Experience** - Intuitive, responsive, non-intrusive design
3. **Code Simplicity** - Clean, maintainable, error-free code
4. **Modular Design** - Reusable components and services
5. **Natural Language Explaining** - Communicate with owner with natural language and explain the tech, programming, flow, components, module and variables that involved in detail.

## RULE CATEGORY 1: USER VALUE FIRST DEVELOPMENT

### Rule 1.1: User Value Validation
**Requirement**: Every feature must pass user value assessment before development
**Enforcement**: 
- [ ] User story with clear benefit statement required
- [ ] Feature must solve identified user problem
- [ ] No features without explicit user signal or request
**Example**: Phase 1 pinning enhancement - user explicitly pins → system provides intelligent organization

### Rule 1.2: Just-in-Time Intelligence
**Requirement**: No background processing without explicit user signals
**Enforcement**:
- [ ] All intelligence features triggered by user actions only
- [ ] No autonomous data collection or processing
- [ ] User controls when and how data is processed
**Example**: Intelligence extraction only happens when user pins message, not continuously

### Rule 1.3: Local-First Architecture
**Requirement**: Minimize external dependencies and API calls
**Enforcement**:
- [ ] All processing happens locally when possible
- [ ] External calls must be justified and documented
- [ ] Offline functionality must be preserved
- [ ] Bundle size impact assessment for new dependencies

## RULE CATEGORY 2: CODE TIDINESS STANDARDS

### Rule 2.1: Variable Naming Convention
**Requirement**: Prevent duplicate variables through systematic naming
**Pattern**: `[context]_[purpose]_[type]` or `[component][Purpose][Type]`
**Examples**:
- ✅ `messagePin_state`, `vaultSuggestion_modal`, `intelligence_extraction_data`
- ❌ `state`, `modal`, `data` (too generic, causes duplicates)
**Enforcement**:
- [ ] ESLint rule for variable naming patterns
- [ ] Pre-commit hook validation
- [ ] Code review checklist item

### Rule 2.2: FontAwesome Icon Management
**Requirement**: Centralized icon management to use local downloaded  icons.  Use single service to prevent massive errors.
**Implementation**:
- [ ] Single icon registry file: `src/components/Icons/index.ts`
- [ ] All icons imported from registry only
- [ ] No direct FontAwesome imports in components, ensure no embedded/CDN from the internet
- [ ] Downloaded icons and must be offline accessible
- [ ] **Icon Storage Location**: All downloaded icons must be placed in `\src\components\Icons` folder
- [ ] **Offline Accessibility**: All icons must be offline accessible through the local folder `\src\components\Icons`
- [ ] **Development Icons Source**: Discover development icons from `\src\components\Icons\fontawesome-free-7.0.0-web` folder or `\src\components\Icons\fontawesome-free-7.0.0-desktop`
- [ ] **Only Icons in Use**: Only icons that are actively used in the application should be stored locally, through the local folder `\src\components\Icons`  I will check out when packaging.
**Enforcement**:
Use ESlint to block all connections to FontAwesome CDN.
Remove ESlint when deployment.


### Rule 2.3: Interface Consistency
**Requirement**: Prevent duplicate type definitions
**Implementation**:
- [ ] Single source of truth for all interfaces
- [ ] Global types in `src/types/index.ts`
- [ ] Preload types must match global types
- [ ] Regular interface synchronization checks
**Enforcement**:
- [ ] TypeScript strict mode enabled
- [ ] Automated type consistency validation
- [ ] Pre-commit TypeScript check

## RULE CATEGORY 3: ERROR PREVENTION & BUG LOGGING

### Rule 3.1: Mandatory Bug Logging
**Requirement**: All bugs must be logged with root cause analysis
**Template**:
```markdown
# Bug Report - [Date]
**Issue**: [Description]
**Root Cause**: [Why it happened]
**Prevention**: [How to avoid in future]
**Pattern**: [Similar issues to watch for]
```
**Enforcement**:
- [ ] Bug log entry required for all fixes
- [ ] Root cause analysis mandatory
- [ ] Pattern recognition documentation
- [ ] Monthly bug pattern review

### Rule 3.2: TypeScript Error Zero Tolerance
**Requirement**: No TypeScript errors allowed in codebase
**Implementation**:
- [ ] `npx tsc --noEmit` must return 0 errors
- [ ] Pre-commit hook blocks commits with TS errors
- [ ] End-of-session QA Engineer clearance required
- [ ] Automated error count tracking
**Enforcement**:
- [ ] CI/CD pipeline TypeScript check
- [ ] Development workflow integration
- [ ] Quality gate enforcement

### Rule 3.3: Mistake Pattern Recognition
**Requirement**: Learn from recurring issues to prevent repetition
**Common Patterns Identified**:
- Duplicate variable names
- Missing type definitions
- FontAwesome import inconsistencies
- External dependency bloat
- Repeat pattern of cache version not being detected, make regular purge of cache
- Everytime the code required electron rebuild, but no actions for me.
**Prevention**:
- [ ] Pattern-based linting rules
- [ ] Automated pattern detection
- [ ] Developer education on common mistakes

## RULE CATEGORY 4: DEVELOPMENT WORKFLOW

### Rule 4.1: Pre-Development Checklist
**Before starting any feature**:
- [ ] User value clearly defined
- [ ] UX design approved
- [ ] Technical approach simplified
- [ ] Component reusability considered
- [ ] External dependencies justified
- [ ] Performance impact assessed

### Rule 4.2: Code Review Standards
**Every code change must**:
- [ ] Pass TypeScript compilation
- [ ] Follow naming conventions
- [ ] Include proper error handling
- [ ] Maintain local-first principles
- [ ] Document any external calls
- [ ] Include user value justification

### Rule 4.3: Quality Gates
**Development cannot proceed without**:
- [ ] Zero TypeScript errors
- [ ] User value validation
- [ ] Performance budget compliance
- [ ] Icon import compliance
- [ ] Variable naming compliance
- [ ] Bug log updates (if fixing issues)

## ENFORCEMENT MECHANISMS

### Automated Tools
- **ESLint**: Variable naming, import patterns
- **TypeScript**: Strict compilation checking
- **Bundle Analyzer**: Dependency and size monitoring
- **Pre-commit Hooks**: Automated quality checks
- **Import Linter**: FontAwesome centralization

### Manual Processes
- **Code Reviews**: Human validation of standards
- **QA Engineer Clearance**: End-of-session error checking
- **Bug Log Reviews**: Pattern recognition and prevention
- **User Value Assessment**: Feature justification

### Quality Metrics
- **TypeScript Error Count**: Must be 0
- **Bundle Size**: Monitor for bloat
- **External Dependencies**: Minimize and justify
- **Bug Pattern Frequency**: Track and reduce
- **User Value Score**: Measure feature utility

## IMPLEMENTATION TIMELINE

### Phase 1: Immediate (Week 1)
- [ ] Implement TypeScript zero tolerance
- [ ] Create centralized icon registry
- [ ] Establish bug logging template
- [ ] Add pre-commit TypeScript check

### Phase 2: Automation (Week 2)
- [ ] Configure ESLint rules
- [ ] Set up bundle analyzer
- [ ] Create import linting
- [ ] Implement quality gates

### Phase 3: Integration (Week 3)
- [ ] Full workflow integration
- [ ] Developer training
- [ ] Quality metric tracking
- [ ] Continuous improvement process

## SUCCESS CRITERIA

### Code Quality
- Zero TypeScript errors maintained
- No duplicate variable issues
- Consistent FontAwesome usage
- Minimal external dependencies

### User Value
- All features serve clear user needs
- Just-in-time intelligence pattern followed
- Local-first architecture maintained
- Non-intrusive user experience

### Development Efficiency
- Reduced debugging time
- Faster development cycles
- Fewer recurring issues
- Improved code maintainability

---

**APPROVED AND ACTIVE**: These rules are now mandatory for all ChatLo development. They address all identified issues from Phase 1 and establish systematic prevention of recurring problems while maintaining ChatLo's core principles of user value, local-first architecture, and code quality.

**Implementation Status**: Phase 1 (Immediate) - IN PROGRESS
